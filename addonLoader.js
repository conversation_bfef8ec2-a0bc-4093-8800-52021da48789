import fs from "fs";
import path from "path";
import yaml from "yaml";

export default async function loadAddons(client, watch = false) {
  const addonFolder = path.resolve("./addons");
  
  if (!fs.existsSync(addonFolder)) {
    console.warn(`[ADDON LOADER] Addons folder not found at ${addonFolder}`);
    return;
  }
  
  const addonDirs = fs.readdirSync(addonFolder);

  async function load(dir) {
    const isCoreCommand = dir === 'core-commands';
    const dirPath = path.join(addonFolder, dir);

    // Read global config
    const globalConfigPath = path.resolve("./config.yml");
    let globalConfig = {};
    if (fs.existsSync(globalConfigPath)) {
      try {
        const raw = fs.readFileSync(globalConfigPath, "utf-8");
        globalConfig = yaml.parse(raw);
      } catch (err) {
        console.warn(`[CONFIG] Failed to parse global config:`, err);
      }
    }

    // For core-commands, load all .js files in the directory
    if (isCoreCommand) {
      const commandFiles = fs.readdirSync(dirPath).filter(file => file.endsWith('.js'));
      
      for (const file of commandFiles) {
        const commandPath = path.join(dirPath, file);
        const commandName = path.basename(file, '.js');
        
        // Cleanup previous command if it exists
        const previousCommand = client.addons.get(`${dir}-${commandName}`);
        if (previousCommand && typeof previousCommand.cleanup === "function") {
          try {
            await previousCommand.cleanup(client);
            console.log(`[COMMAND CLEANUP] ${commandName}`);
          } catch (err) {
            console.warn(`[COMMAND CLEANUP ERROR] ${commandName}`, err);
          }
        }

        try {
          const command = await import(commandPath + `?update=${Date.now()}`);
          
          if (command.init && typeof command.init === 'function') {
            await command.init(client, globalConfig);
            client.addons.set(`${dir}-${commandName}`, command);
            console.log(`[COMMAND LOADED] ${commandName}`);
          } else {
            console.warn(`[COMMAND LOADER] No init function found in ${commandName}`);
          }
        } catch (err) {
          console.error(`[COMMAND LOADER] Failed to load command ${commandName}:`, err);
        }
      }
      return;
    }

    // Regular addon loading (non-core-commands)
    const entryFile = path.join(dirPath, "index.js");
    
    if (!fs.existsSync(entryFile)) {
      console.warn(`[ADDON LOADER] Entry file not found: ${entryFile}`);
      return;
    }

    // If addon exists, cleanup before reload
    const previousAddon = client.addons.get(dir);
    if (previousAddon && typeof previousAddon.cleanup === "function") {
      try {
        await previousAddon.cleanup(client);
        console.log(`[ADDON CLEANUP] ${dir}`);
      } catch (err) {
        console.warn(`[ADDON CLEANUP ERROR] ${dir}`, err);
      }
    }

    // Load addon config
    let addonConfig = {};
    const configPath = path.join(dirPath, "config.yml");
    if (fs.existsSync(configPath)) {
      try {
        addonConfig = yaml.parse(fs.readFileSync(configPath, "utf-8"));
      } catch (err) {
        console.warn(`[CONFIG] Failed to parse addon config for ${dir}:`, err);
      }
    }

    try {
      const addon = await import(entryFile + `?update=${Date.now()}`);
      
      if (addon.init && typeof addon.init === 'function') {
        await addon.init(client, { ...globalConfig, ...addonConfig });
        client.addons.set(dir, addon);
        console.log(`[ADDON LOADED] ${dir}`);
      } else {
        console.warn(`[ADDON LOADER] No init function found in ${dir}`);
      }
    } catch (err) {
      console.error(`[ADDON LOADER] Failed to load addon ${dir}:`, err);
    }
  }

  // First load - load all addons
  for (const dir of addonDirs) {
    const dirPath = path.join(addonFolder, dir);
    if (fs.statSync(dirPath).isDirectory()) {
      await load(dir);
    }
  }

  // Hot Reload Watch
  if (watch) {
    console.log(`[ADDON LOADER] Watching for changes in ${addonFolder}`);
    fs.watch(addonFolder, { recursive: true }, async (eventType, filename) => {
      if (!filename || !filename.match(/\.(js|yml)$/)) return;
      
      const pathParts = filename.split(path.sep);
      const dir = pathParts[0];
      
      if (!addonDirs.includes(dir)) return;
      
      console.log(`[RELOAD] Changes detected in ${dir} (${filename})`);
      
      // Debounce rapid file changes
      clearTimeout(load.timeout);
      load.timeout = setTimeout(async () => {
        await load(dir);
      }, 100);
    });
  }
}