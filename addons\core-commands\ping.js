export async function init(client) {
    client.commands.set('ping', {
        data: {
            name: 'ping',
            description: 'Check the bot\'s latency',
        },
        async execute(interaction) {
            const sent = await interaction.reply({ content: 'Pinging...', withResponse: true });
            const latency = sent.createdTimestamp - interaction.createdTimestamp;
            await interaction.editReply(`🏓 Pong! Latency is ${latency}ms. API Latency is ${Math.round(client.ws.ping)}ms`);
        }
    });

    client.logger.info("[CORE COMMANDS] Ping command loaded!");
}

export function cleanup(client) {
    client.commands.delete('ping');
    client.logger.info("[CORE COMMANDS] Ping command unloaded!");
}
