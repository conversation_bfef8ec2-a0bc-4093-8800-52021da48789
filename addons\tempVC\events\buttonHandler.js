import { ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder, StringSelectMenuBuilder, StringSelectMenuOptionBuilder, MessageFlags, ButtonBuilder, ButtonStyle, UserSelectMenuBuilder, PermissionFlagsBits } from 'discord.js';
import { ChannelUtils } from '../utils/channelUtils.js';
import { createControlPanel } from '../index.js';

export async function handleVCButtonInteraction(interaction, client) {
    // Early checks to prevent null pointer exceptions
    if (!interaction) {
        client.logger.error('Button/Select interaction is null or undefined');
        return;
    }

    // Handle select menu interactions
    if (interaction.isUserSelectMenu() || interaction.isStringSelectMenu()) {
        // Check for region select menu
        if (interaction.customId === 'vc_region_select') {
            return await handleRegionSelect(interaction, client);
        }
        
        // Check for transfer ownership select menu
        if (interaction.customId === 'vc_transfer_ownership_select') {
            return await handleTransferOwnershipSelect(interaction, client);
        }
        
        // Other select menu interactions
        return await handleSelectMenuInteraction(interaction, client);
    }

    // Existing button interaction logic...
    if (!interaction.isButton()) return;

    const voiceChannel = interaction.member?.voice?.channel;
    if (!voiceChannel || !ChannelUtils.isTempChannel(voiceChannel.id)) {
        await interaction.deferReply({ flags: MessageFlags.Ephemeral });
        
        // Try to sync and recover before giving up
        try {
            await ChannelUtils.syncTempChannelsWithDatabase(client);
            
            // Additional null checks
            const currentVoiceChannel = interaction.member?.voice?.channel;
            if (!currentVoiceChannel) {
                await interaction.editReply({
                    content: '❌ You must be in a temporary voice channel to use this!',
                });
                return;
            }

            if (!ChannelUtils.isTempChannel(currentVoiceChannel.id)) {
                await interaction.editReply({
                    content: '❌ You must be in a temporary voice channel to use this!',
                });
                return;
            }
        } catch (syncError) {
            client.logger.error('Failed to sync during button interaction:', syncError);
            await interaction.editReply({
                content: '❌ You must be in a temporary voice channel to use this!',
            });
            return;
        }
    }

    const tempChannelData = ChannelUtils.getTempChannel(voiceChannel.id);
    // Allow Administrator or Manage Channels permission to bypass owner check
    if (!tempChannelData || (tempChannelData.ownerId !== interaction.user.id &&
        !interaction.member.permissions.has(PermissionFlagsBits.Administrator) &&
        !interaction.member.permissions.has(PermissionFlagsBits.ManageChannels))) {
        await interaction.deferReply({ flags: MessageFlags.Ephemeral });
        await interaction.editReply({
            content: '❌ Only the channel owner or a staff/admin can use these controls!',
        });
        return;
    }

    try {
        switch (interaction.customId) {
            case 'vc_lock':
                await handleLockToggle(interaction, voiceChannel, client);
                break;
            case 'vc_hide':
                await handleHideToggle(interaction, voiceChannel, client);
                break;
            case 'vc_settings':
                await handleSettingsModal(interaction, voiceChannel, client);
                break;
            case 'vc_access':
                await handleAccessModal(interaction, voiceChannel, client);
                break;
            case 'vc_regions':
                await handleRegionsButton(interaction, voiceChannel, client);
                break;
            case 'vc_claim':
                await handleClaimChannel(interaction, voiceChannel, client);
                break;
            case 'vc_transfer_ownership':
                await handleTransferOwnership(interaction, voiceChannel, client);
                break;
            case 'vc_kick_user':
                await handleKickUserModal(interaction, voiceChannel, client);
                break;
            case 'vc_block_user':
                await handleBlockUserModal(interaction, voiceChannel, 'join', client);
                break;
            case 'vc_unblock_user':
                await handleUnblockUserModal(interaction, voiceChannel, client);
                break;
            case 'vc_kick_manual_id_button':
                await showManualIdModal(interaction, 'kick', client);
                break;
            case 'vc_block_manual_id_button:join':
                await showManualIdModal(interaction, 'block:join', client);
                break;
            case 'vc_block_manual_id_button:view':
                await showManualIdModal(interaction, 'block:view', client);
                break;
            case 'vc_unblock_manual_id_button':
                await showManualIdModal(interaction, 'unblock', client);
                break;
            default:
                client.logger.error(`Unknown button interaction: ${interaction.customId}`);
                await interaction.deferReply({ flags: MessageFlags.Ephemeral });
                await interaction.editReply({
                    content: '❌ Unknown button interaction!',
                });
        }
    }
    catch (error) {
        client.logger.error('Button interaction error:', error);
        
        // Log additional details about the error
        client.logger.error('Error details:', {
            customId: interaction.customId,
            channelId: voiceChannel.id,
            errorName: error.name,
            errorMessage: error.message,
            errorStack: error.stack
        });

        // Check if the interaction has already been replied to
        if (interaction.deferred || interaction.replied) {
            try {
                await interaction.editReply({
                    content: '❌ An error occurred while processing your request.',
                    flags: MessageFlags.Ephemeral,
                });
            } catch (editError) {
                client.logger.error('Failed to edit reply:', editError);
            }
        } else {
            try {
        await interaction.reply({ 
                    content: '❌ An error occurred while processing your request.',
                    flags: MessageFlags.Ephemeral,
                });
            } catch (replyError) {
                client.logger.error('Failed to reply to interaction:', replyError);
            }
        }
    }
}

async function handleLockToggle(interaction, voiceChannel, client) {
    await interaction.deferUpdate(); // Change to deferUpdate
    const tempChannelData = ChannelUtils.getTempChannel(voiceChannel.id);
    const isLocked = tempChannelData?.settings.locked;

    try {
        if (isLocked) {
            // Unlock the channel: allow @everyone to connect
            await voiceChannel.permissionOverwrites.edit(voiceChannel.guild.roles.everyone, {
                Connect: null, // Inherit permissions or allow by default
            });
            // Ensure owner can connect (even if already null, ensures consistency)
            await voiceChannel.permissionOverwrites.edit(interaction.user.id, {
                Connect: true,
            });
            tempChannelData.settings.locked = false;
            await interaction.followUp({
                content: '🔓 Channel unlocked successfully!',
                flags: MessageFlags.Ephemeral,
            });
        }
        else {
            // Lock the channel: deny @everyone from connecting
            await voiceChannel.permissionOverwrites.edit(voiceChannel.guild.roles.everyone, {
                Connect: false,
            });
            // Explicitly allow owner to connect
            await voiceChannel.permissionOverwrites.edit(interaction.user.id, {
                Connect: true,
            });
            tempChannelData.settings.locked = true;
            await interaction.followUp({
                content: '🔒 Channel locked successfully!',
                flags: MessageFlags.Ephemeral,
            });
        }
    }
    catch (error) {
        client.logger.error('Failed to toggle channel lock:', error);
        await interaction.followUp({
            content: '❌ Failed to change channel lock status. Please check bot permissions.',
            flags: MessageFlags.Ephemeral,
        });
    }
}

async function handleHideToggle(interaction, voiceChannel, client) {
    await interaction.deferUpdate(); // Defer the interaction immediately

    const tempChannelData = ChannelUtils.getTempChannel(voiceChannel.id);
    const isHidden = tempChannelData?.settings.hidden;

    try {
        // Ensure we have the latest channel data
        await voiceChannel.fetch();

        if (isHidden) {
            // Unhide the channel
            await voiceChannel.permissionOverwrites.edit(voiceChannel.guild.roles.everyone, {
                ViewChannel: null
            });

            // Explicitly set ViewChannel for the owner (even if it's already null, this ensures consistency)
            await voiceChannel.permissionOverwrites.edit(interaction.user.id, {
                ViewChannel: true
            });

            // Update the reply message
            await interaction.followUp({
                content: '👁️ Channel is now visible!',
                flags: MessageFlags.Ephemeral,
            });
        }
        else {
            // Hide the channel
            await voiceChannel.permissionOverwrites.edit(voiceChannel.guild.roles.everyone, {
                ViewChannel: false
            });

            // Explicitly set ViewChannel for the owner
            await voiceChannel.permissionOverwrites.edit(interaction.user.id, {
                ViewChannel: true
            });

            // Update the reply message
            await interaction.followUp({
                content: '🫥 Channel is now hidden!',
                flags: MessageFlags.Ephemeral,
            });
        }

        // Update the hidden state in temp channel data
        if (tempChannelData) {
            tempChannelData.settings.hidden = !isHidden;
        }
    }
    catch (error) {
        client.logger.error('Failed to toggle channel visibility:', error);

        // Try to send a followUp message (since we deferred)
        try {
            await interaction.followUp({
                content: '❌ Failed to change channel visibility. Please check bot permissions and ensure you have permission to view the channel.',
                flags: MessageFlags.Ephemeral,
            });
        } catch (followUpError) {
            client.logger.error('Failed to send error followUp:', followUpError);
        }

        // Rethrow the original error to be caught by the main interaction handler
        throw error;
    }
}

// Placeholder functions for new button interactions
async function handleSettingsModal(interaction, voiceChannel, client) {
    const tempChannelData = ChannelUtils.getTempChannel(voiceChannel.id);
    
    // Create the settings modal
    const modal = new ModalBuilder()
        .setCustomId('vc_settings_modal')
        .setTitle('🔧 Advanced Channel Settings');

    // Channel Name Input
    const nameInput = new TextInputBuilder()
        .setCustomId('name_input')
        .setLabel('Channel Name')
        .setStyle(TextInputStyle.Short)
        .setValue(voiceChannel.name)
        .setMaxLength(32)
        .setPlaceholder('Enter channel name')
        .setRequired(true);

    // User Limit Input
    const limitInput = new TextInputBuilder()
        .setCustomId('limit_input')
        .setLabel('User Limit')
        .setStyle(TextInputStyle.Short)
        .setValue(voiceChannel.userLimit.toString())
        .setMaxLength(2)
        .setPlaceholder('0-99 (0 = unlimited)')
        .setRequired(true);

    // Bitrate Input
    const bitrateInput = new TextInputBuilder()
        .setCustomId('bitrate_input')
        .setLabel('Bitrate (kbps)')
        .setStyle(TextInputStyle.Short)
        .setValue((voiceChannel.bitrate / 1000).toString())
        .setMaxLength(3)
        .setPlaceholder('8-384')
        .setRequired(true);

    // NSFW Toggle Input
    const nsfwInput = new TextInputBuilder()
        .setCustomId('nsfw_input')
        .setLabel('NSFW Status')
        .setStyle(TextInputStyle.Short)
        .setValue(tempChannelData?.settings.nsfw ? 'yes' : 'no')
        .setPlaceholder('yes/no')
        .setMaxLength(3)
        .setRequired(true);

    // Create action rows for the modal
    const nameRow = new ActionRowBuilder().addComponents(nameInput);
    const limitRow = new ActionRowBuilder().addComponents(limitInput);
    const bitrateRow = new ActionRowBuilder().addComponents(bitrateInput);
    const nsfwRow = new ActionRowBuilder().addComponents(nsfwInput);

    // Add components to the modal
    modal.addComponents(nameRow, limitRow, bitrateRow, nsfwRow);

    // Show the modal
    await interaction.showModal(modal);
}

async function handleSettingsModalSubmission(interaction, client) {
    // Early checks
    if (!interaction.isModalSubmit() || interaction.customId !== 'vc_settings_modal') return;

    // Log all available fields for debugging
    client.logger.error('Modal Fields:', {
        customIds: Array.from(interaction.fields.fields.keys()),
        values: Array.from(interaction.fields.fields.entries()).map(([key, value]) => ({ key, value: value.value }))
    });

    const voiceChannel = interaction.member?.voice?.channel;
    if (!voiceChannel || !ChannelUtils.isTempChannel(voiceChannel.id)) {
        await interaction.deferReply({ flags: MessageFlags.Ephemeral });
        await interaction.editReply({
            content: '❌ You must be in a temporary voice channel to modify settings!',
        });
        return;
    }

    try {
        // Extract input values with error handling
        let channelName, userLimit, bitrate, nsfwStatus;
        
        try {
            channelName = interaction.fields.getTextInputValue('name_input').trim();
        } catch (nameError) {
            client.logger.error('Error getting name input:', nameError);
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ Failed to get channel name. Please try again.',
                flags: MessageFlags.Ephemeral,
            });
            return;
        }

        try {
            userLimit = parseInt(interaction.fields.getTextInputValue('limit_input'), 10);
        } catch (limitError) {
            client.logger.error('Error getting user limit:', limitError);
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ Failed to get user limit. Please try again.',
                flags: MessageFlags.Ephemeral,
            });
            return;
        }

        try {
            bitrate = parseInt(interaction.fields.getTextInputValue('bitrate_input'), 10);
        } catch (bitrateError) {
            client.logger.error('Error getting bitrate:', bitrateError);
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ Failed to get bitrate. Please try again.',
                flags: MessageFlags.Ephemeral,
            });
            return;
        }

        try {
            nsfwStatus = interaction.fields.getTextInputValue('nsfw_input').toLowerCase();
        } catch (nsfwError) {
            client.logger.error('Error getting NSFW status:', nsfwError);
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ Failed to get NSFW status. Please try again.',
                flags: MessageFlags.Ephemeral,
            });
            return;
        }

        // Validate inputs
        if (channelName.length === 0 || channelName.length > 32) {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ Channel name must be 1-32 characters long.',
                flags: MessageFlags.Ephemeral,
            });
            return;
        }

        // Validate user limit
        if (isNaN(userLimit) || userLimit < 0 || userLimit > 99) {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ User limit must be between 0 and 99.',
                flags: MessageFlags.Ephemeral,
            });
            return;
        }

        // Validate bitrate
        if (isNaN(bitrate) || bitrate < 8 || bitrate > 384) {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ Bitrate must be between 8 and 384 kbps.',
                flags: MessageFlags.Ephemeral,
            });
            return;
        }

        // Validate NSFW status
        const isNsfw = ['yes', 'y', 'true', '1'].includes(nsfwStatus);

        // Apply changes
        try {
            // Update channel name
            await voiceChannel.setName(channelName);

            // Update user limit
            await ChannelUtils.setUserLimit(voiceChannel, userLimit);

            // Update bitrate
            await ChannelUtils.setBitrate(voiceChannel, bitrate * 1000);

            // Update NSFW status
            await ChannelUtils.setNsfw(voiceChannel.id, voiceChannel, isNsfw, client);

            // Confirm changes
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '✅ Channel settings updated successfully!',
                flags: MessageFlags.Ephemeral,
            });
        } catch (updateError) {
            client.logger.error('Failed to update channel settings:', updateError);
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ Failed to update channel settings. Check bot permissions.',
                flags: MessageFlags.Ephemeral,
            });
        }
    } catch (error) {
        client.logger.error('Settings modal submission error:', error);
        
        try {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ An error occurred while processing your request.',
                flags: MessageFlags.Ephemeral,
            });
        } catch (replyError) {
            client.logger.error('Failed to send error reply:', replyError);
        }
    }
}

async function handleAccessModal(interaction, voiceChannel, client) {
    await interaction.deferReply({ flags: MessageFlags.Ephemeral });

    const blockedUsers = ChannelUtils.getBlockedUsers(voiceChannel.id);
    const totalBlockedUsers = new Set([...blockedUsers.join, ...blockedUsers.view]).size;

    const kickButton = new ButtonBuilder()
        .setCustomId('vc_kick_user')
        .setLabel('Kick User')
        .setStyle(ButtonStyle.Danger);

    const blockButton = new ButtonBuilder()
        .setCustomId('vc_block_user')
        .setLabel('Block User')
        .setStyle(ButtonStyle.Secondary);

    const unblockButton = new ButtonBuilder()
        .setCustomId('vc_unblock_user')
        .setLabel('Unblock User')
        .setStyle(ButtonStyle.Success);

    const actionRow = new ActionRowBuilder()
        .addComponents(kickButton, blockButton, unblockButton);

    await interaction.editReply({
        content: `## 👥 Access Management\n**Blocked:** ${totalBlockedUsers} | **Members:** ${voiceChannel.members.size}`,
        components: [actionRow],
    });
}

async function handleKickUserModal(interaction, voiceChannel, client) {
    await interaction.deferUpdate();

    const membersInVc = voiceChannel.members
        .filter(member => !member.user.bot && member.id !== interaction.user.id) // Exclude bots and the owner
        .map(member => ({
            label: member.displayName,
            value: member.id,
        }));

    const selectMenu = new UserSelectMenuBuilder()
        .setCustomId('vc_kick_user_select')
        .setPlaceholder('Select a user to kick...')
        .setMinValues(1)
        .setMaxValues(1);

    const manualInputButton = new ButtonBuilder()
        .setCustomId('vc_kick_manual_id_button')
        .setLabel('Enter Manual ID')
        .setStyle(ButtonStyle.Secondary);

    const actionRowSelect = new ActionRowBuilder().addComponents(selectMenu);
    const actionRowButton = new ActionRowBuilder().addComponents(manualInputButton);

    await interaction.editReply({
        content: 'Select user to kick or enter ID manually:',
        components: [actionRowSelect, actionRowButton],
        flags: MessageFlags.Ephemeral,
    });
}

async function handleBlockUserModal(interaction, voiceChannel, blockType = 'join', client) {
    await interaction.deferUpdate();

    const membersInVc = voiceChannel.members
        .filter(member => !member.user.bot && member.id !== interaction.user.id) // Exclude bots and the owner
        .map(member => ({
            label: member.displayName,
            value: member.id,
        }));

    const selectMenu = new UserSelectMenuBuilder()
        .setCustomId(`vc_block_user_select:${blockType}`)
        .setPlaceholder(`Select a user to block from ${blockType === 'join' ? 'joining' : 'viewing'}...`)
        .setMinValues(1)
        .setMaxValues(1);

    const manualInputButton = new ButtonBuilder()
        .setCustomId(`vc_block_manual_id_button:${blockType}`)
        .setLabel('Enter Manual ID')
        .setStyle(ButtonStyle.Secondary);

    const actionRowSelect = new ActionRowBuilder().addComponents(selectMenu);
    const actionRowButton = new ActionRowBuilder().addComponents(manualInputButton);

    await interaction.editReply({
        content: `Select user to block from ${blockType === 'join' ? 'joining' : 'viewing'} or enter ID manually:`,
        components: [actionRowSelect, actionRowButton],
        flags: MessageFlags.Ephemeral,
    });
}

async function handleUnblockUserModal(interaction, voiceChannel, client) {
    await interaction.deferUpdate();

    const blockedUsers = ChannelUtils.getBlockedUsers(voiceChannel.id);
    const allBlockedUsers = [...new Set([...blockedUsers.join, ...blockedUsers.view])];

    if (allBlockedUsers.length === 0) {
        await interaction.editReply({
            content: '✅ No users are currently blocked.',
            flags: MessageFlags.Ephemeral,
            components: [], // Remove components if no users to unblock
        });
        return;
    }

    const unblockOptions = await Promise.all(allBlockedUsers.map(async (userId) => {
        const user = await interaction.client.users.fetch(userId).catch(() => null);
        return {
            label: user ? user.tag : `Unknown User (${userId})`,
            value: userId,
        };
    }));

    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('vc_unblock_user_select')
        .setPlaceholder('Select a user to unblock...')
        .addOptions(unblockOptions.map(option => 
            new StringSelectMenuOptionBuilder()
                .setLabel(option.label)
                .setValue(option.value)
        ));

    const actionRowSelect = new ActionRowBuilder().addComponents(selectMenu);

    await interaction.editReply({
        content: 'Select user to unblock:',
        components: [actionRowSelect],
        flags: MessageFlags.Ephemeral,
    });
}

// Helper function to show a modal for manual user ID input
async function showManualIdModal(interaction, type, client) {
    const modal = new ModalBuilder()
        .setCustomId(`vc_manual_id_modal:${type}`)
        .setTitle(`Enter User ID for ${type.replace(':', ' ').replace('block', 'Block').replace('kick', 'Kick').replace('unblock', 'Unblock')}`);

    const userInput = new TextInputBuilder()
        .setCustomId('user_input')
        .setLabel('User ID')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Enter user ID (e.g., 123456789012345678)')
        .setRequired(true);

    modal.addComponents(new ActionRowBuilder().addComponents(userInput));
    await interaction.showModal(modal);
}

async function handleRegionsButton(interaction, voiceChannel, client) {
    // Define available regions
    const regions = [
        { name: 'Automatic', value: null },
        { name: 'US West', value: 'us-west' },
        { name: 'US East', value: 'us-east' },
        { name: 'US Central', value: 'us-central' },
        { name: 'US South', value: 'us-south' },
        { name: 'Rotterdam', value: 'rotterdam' },
        { name: 'Singapore', value: 'singapore' },
        { name: 'Sydney', value: 'sydney' },
        { name: 'Japan', value: 'japan' },
        { name: 'Hong Kong', value: 'hongkong' },
        { name: 'South Korea', value: 'south-korea' },
        { name: 'India', value: 'india' },
        { name: 'Brazil', value: 'brazil' },
        { name: 'South Africa', value: 'southafrica' },
    ];

    // Create the select menu
    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('vc_region_select')
        .setPlaceholder('Choose a voice region...')
        .addOptions(regions.map((region) => 
            new StringSelectMenuOptionBuilder()
                .setLabel(region.name)
                .setValue(region.value || 'auto')
                .setDefault(voiceChannel.rtcRegion === region.value)
        ));

    const actionRow = new ActionRowBuilder().addComponents(selectMenu);

    // Defer the reply to show the select menu
    await interaction.deferReply({ flags: MessageFlags.Ephemeral });
    await interaction.editReply({
        content: `## 🌍 Voice Region\n**Current:** ${voiceChannel.rtcRegion || 'Auto'}`,
        components: [actionRow],
        flags: MessageFlags.Ephemeral,
    });
}

async function handleRegionSelect(interaction, client) {
    // Early checks
    if (!interaction.isStringSelectMenu() || interaction.customId !== 'vc_region_select') return;

    const voiceChannel = interaction.member?.voice?.channel;
    if (!voiceChannel || !ChannelUtils.isTempChannel(voiceChannel.id)) {
        await interaction.deferReply({ flags: MessageFlags.Ephemeral });
        await interaction.editReply({
            content: '❌ You must be in a temporary voice channel to change the region!',
        });
        return;
    }

    // Get the selected region
    const selectedRegion = interaction.values[0];

    try {
        // Check permissions (only owner or staff can change region)
        const tempChannelData = ChannelUtils.getTempChannel(voiceChannel.id);
        if (!tempChannelData || (tempChannelData.ownerId !== interaction.user.id &&
            !interaction.member.permissions.has(PermissionFlagsBits.Administrator) &&
            !interaction.member.permissions.has(PermissionFlagsBits.ManageChannels))) {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ Only the channel owner or a staff/admin can change the region!',
                flags: MessageFlags.Ephemeral,
            });
            return;
        }

        // Set the region
        const regionToSet = selectedRegion === 'auto' ? null : selectedRegion;
        await voiceChannel.setRTCRegion(regionToSet);

        // Confirm the change
        await interaction.deferReply({ flags: MessageFlags.Ephemeral });
        await interaction.editReply({
            content: `✅ Channel region updated to: ${selectedRegion === 'auto' ? 'Automatic' : selectedRegion}`,
            flags: MessageFlags.Ephemeral,
        });

        // Log the region change
        client.logger.info(`Set region of channel ${voiceChannel.name} to ${selectedRegion}`);
    } catch (error) {
        client.logger.error(`Failed to set region for channel ${voiceChannel.name}:`, error);
        
        try {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ Failed to update channel region. Check bot permissions.',
                flags: MessageFlags.Ephemeral,
            });
        } catch (replyError) {
            client.logger.error('Failed to send error reply:', replyError);
        }
    }
}

async function handleClaimChannel(interaction, voiceChannel, client) {
    await interaction.deferReply({ flags: MessageFlags.Ephemeral });
    
    try {
        // Check if channel can be claimed
        if (!ChannelUtils.canChannelBeClaimed(voiceChannel.id, client)) {
            await interaction.editReply({
                content: '❌ This channel cannot be claimed. The owner must leave the channel first.',
                flags: MessageFlags.Ephemeral,
            });
            return;
        }

        // Check if user is in the channel
        if (!voiceChannel.members.has(interaction.user.id)) {
            await interaction.editReply({
                content: '❌ You must be in the channel to claim it.',
                flags: MessageFlags.Ephemeral,
            });
            return;
        }

        // Claim the channel
        const tempChannelData = await ChannelUtils.claimChannel(voiceChannel.id, interaction.user.id, client);

        // Get the new owner member object
        const newOwner = interaction.member;

        // Update the control panel
        await interaction.editReply({
            content: `👑 **Channel claimed successfully!**\n\nYou are now the owner of **${voiceChannel.name}**`,
            flags: MessageFlags.Ephemeral,
        });

        // Recreate the control panel
        try {
            await createControlPanel(voiceChannel, client);
        } catch (updateError) {
            client.logger.error('Failed to update control panel after claim:', updateError);
        }

        client.logger.info(`Channel ${voiceChannel.name} (${voiceChannel.id}) claimed by ${interaction.user.tag} (${interaction.user.id})`);

    } catch (error) {
        client.logger.error('Failed to claim channel:', error);
        await interaction.editReply({
            content: `❌ Failed to claim channel: ${error.message}`,
            flags: MessageFlags.Ephemeral,
        });
    }
}

async function handleTransferOwnership(interaction, voiceChannel, client) {
    await interaction.deferReply({ flags: MessageFlags.Ephemeral });

    try {
        // Check if user is the current owner or has admin permissions
        const tempChannelData = ChannelUtils.getTempChannel(voiceChannel.id);
        if (!tempChannelData || (tempChannelData.ownerId !== interaction.user.id &&
            !interaction.member.permissions.has(PermissionFlagsBits.Administrator) &&
            !interaction.member.permissions.has(PermissionFlagsBits.ManageChannels))) {
            await interaction.editReply({
                content: '❌ Only the channel owner or a staff/admin can transfer ownership!',
                flags: MessageFlags.Ephemeral,
            });
            return;
        }

        // Get all members in the voice channel (excluding the current owner)
        const membersInChannel = voiceChannel.members.filter(member => member.id !== interaction.user.id);
        
        if (membersInChannel.size === 0) {
            await interaction.editReply({
                content: '❌ No other users in the channel to transfer ownership to.',
                flags: MessageFlags.Ephemeral,
            });
            return;
        }

        // Create options for the select menu
        const memberOptions = membersInChannel.map(member => ({
            label: member.displayName,
            value: member.id,
            description: `@${member.user.tag}`
        }));

        // Create the select menu
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('vc_transfer_ownership_select')
            .setPlaceholder('Select a user to transfer ownership to...')
            .addOptions(memberOptions.map(option => 
                new StringSelectMenuOptionBuilder()
                    .setLabel(option.label)
                    .setValue(option.value)
                    .setDescription(option.description)
            ));

        const actionRow = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.editReply({
            content: '👑 **Transfer Ownership**\n\nSelect a user from the channel to transfer ownership to:',
            components: [actionRow],
            flags: MessageFlags.Ephemeral,
        });

    } catch (error) {
        client.logger.error('Failed to show transfer ownership menu:', error);
        await interaction.editReply({
            content: `❌ Failed to show transfer ownership menu: ${error.message}`,
            flags: MessageFlags.Ephemeral,
        });
    }
}

async function handleTransferOwnershipSelect(interaction, client) {
    // Early checks
    if (!interaction.isStringSelectMenu() || interaction.customId !== 'vc_transfer_ownership_select') return;

    const voiceChannel = interaction.member?.voice?.channel;
    if (!voiceChannel || !ChannelUtils.isTempChannel(voiceChannel.id)) {
        await interaction.deferReply({ flags: MessageFlags.Ephemeral });
        await interaction.editReply({
            content: '❌ You must be in a temporary voice channel to transfer ownership!',
        });
        return;
    }

    try {
        const selectedUserId = interaction.values[0];
        
        // Check permissions (only current owner or admin can transfer)
        const tempChannelData = ChannelUtils.getTempChannel(voiceChannel.id);
        if (!tempChannelData || (tempChannelData.ownerId !== interaction.user.id &&
            !interaction.member.permissions.has(PermissionFlagsBits.Administrator) &&
            !interaction.member.permissions.has(PermissionFlagsBits.ManageChannels))) {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ Only the channel owner or a staff/admin can transfer ownership!',
                flags: MessageFlags.Ephemeral,
            });
            return;
        }

        // Get the new owner member
        const newOwner = voiceChannel.guild.members.cache.get(selectedUserId);
        if (!newOwner) {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ Selected user not found in the guild.',
                flags: MessageFlags.Ephemeral,
            });
            return;
        }

        // Check if the new owner is still in the channel
        if (!voiceChannel.members.has(selectedUserId)) {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ Selected user is no longer in the channel.',
                flags: MessageFlags.Ephemeral,
            });
            return;
        }

        // Transfer ownership using the existing claimChannel function
        await ChannelUtils.claimChannel(voiceChannel.id, selectedUserId, client);

        // Update the control panel
        try {
            await createControlPanel(voiceChannel, client);
        } catch (updateError) {
            client.logger.error('Failed to update control panel after ownership transfer:', updateError);
        }

        await interaction.deferReply({ flags: MessageFlags.Ephemeral });
        await interaction.editReply({
            content: `👑 **Ownership transferred successfully!**\n\n**${newOwner.displayName}** is now the owner of **${voiceChannel.name}**`,
            flags: MessageFlags.Ephemeral,
        });

        client.logger.info(`Channel ${voiceChannel.name} (${voiceChannel.id}) ownership transferred from ${interaction.user.tag} to ${newOwner.tag}`);

    } catch (error) {
        client.logger.error('Failed to transfer ownership:', error);
        await interaction.deferReply({ flags: MessageFlags.Ephemeral });
        await interaction.editReply({
            content: `❌ Failed to transfer ownership: ${error.message}`,
            flags: MessageFlags.Ephemeral,
        });
    }
}

// New function to handle select menu interactions
async function handleSelectMenuInteraction(interaction, client) {
    const voiceChannel = interaction.member?.voice?.channel;
    if (!voiceChannel || !ChannelUtils.isTempChannel(voiceChannel.id)) {
        await interaction.deferReply({ flags: MessageFlags.Ephemeral });
        await interaction.editReply({
            content: '❌ You must be in a temporary voice channel to use this!',
        });
        return;
    }

    try {
        switch (interaction.customId) {
            case 'vc_kick_user_select': {
                const targetUserId = interaction.values[0];
                const targetMember = voiceChannel.members.get(targetUserId);
                
                if (!targetMember) {
                    await interaction.deferReply({ flags: MessageFlags.Ephemeral });
                    await interaction.editReply({
                        content: '❌ User is no longer in the voice channel.',
                        flags: MessageFlags.Ephemeral,
                    });
                    return;
                }

                // Kick the user from the voice channel
                try {
                    await targetMember.voice.disconnect('Kicked from temporary voice channel');
                    
                    // Add kick protection
                    ChannelUtils.addKickProtection(voiceChannel.id, targetUserId);

                    await interaction.deferReply({ flags: MessageFlags.Ephemeral });
                    await interaction.editReply({
                        content: `🦵 Successfully kicked ${targetMember.displayName} from the channel.`,
                        flags: MessageFlags.Ephemeral,
                    });
                } catch (kickError) {
                    client.logger.error('Failed to kick user:', kickError);
                    await interaction.deferReply({ flags: MessageFlags.Ephemeral });
                    await interaction.editReply({
                        content: '❌ Failed to kick user. Check bot permissions.',
                        flags: MessageFlags.Ephemeral,
                    });
                }
                break;
            }
            case 'vc_block_user_select:join':
            case 'vc_block_user_select:view': {
                const targetUserId = interaction.values[0];
                const blockType = interaction.customId.split(':')[1]; // 'join' or 'view'

                // Block the user
                ChannelUtils.blockUser(voiceChannel.id, targetUserId, blockType);

                // Apply permission overwrites to block the user
                try {
                    await voiceChannel.permissionOverwrites.edit(targetUserId, {
                        Connect: blockType === 'join' ? false : undefined,
                        ViewChannel: blockType === 'view' ? false : undefined,
                    });

                    await interaction.deferReply({ flags: MessageFlags.Ephemeral });
                    await interaction.editReply({
                        content: `🚫 Successfully blocked user from ${blockType === 'join' ? 'joining' : 'viewing'} the channel.`,
                        flags: MessageFlags.Ephemeral,
                    });
                } catch (blockError) {
                    client.logger.error('Failed to block user:', blockError);
                    await interaction.deferReply({ flags: MessageFlags.Ephemeral });
                    await interaction.editReply({
                        content: '❌ Failed to block user. Check bot permissions.',
                        flags: MessageFlags.Ephemeral,
                    });
                }
                break;
            }
            case 'vc_unblock_user_select': {
                const targetUserId = interaction.values[0];

                // Unblock the user from both join and view
                ChannelUtils.unblockUser(voiceChannel.id, targetUserId, 'join');
                ChannelUtils.unblockUser(voiceChannel.id, targetUserId, 'view');

                // Remove permission overwrites
                try {
                    await voiceChannel.permissionOverwrites.edit(targetUserId, {
                        Connect: null,
                        ViewChannel: null,
                    });

                    await interaction.deferReply({ flags: MessageFlags.Ephemeral });
                    await interaction.editReply({
                        content: '✅ Successfully unblocked the user.',
                        flags: MessageFlags.Ephemeral,
                    });
                } catch (unblockError) {
                    client.logger.error('Failed to unblock user:', unblockError);
                    await interaction.deferReply({ flags: MessageFlags.Ephemeral });
                    await interaction.editReply({
                        content: '❌ Failed to unblock user. Check bot permissions.',
                        flags: MessageFlags.Ephemeral,
                    });
                }
                break;
            }
            default:
                client.logger.error(`Unknown select menu interaction: ${interaction.customId}`);
                await interaction.deferReply({ flags: MessageFlags.Ephemeral });
                await interaction.editReply({
                    content: '❌ Unknown interaction!',
                    flags: MessageFlags.Ephemeral,
                });
        }
    } catch (error) {
        client.logger.error('Select menu interaction error:', error);
        
        try {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ An error occurred while processing your request.',
                flags: MessageFlags.Ephemeral,
            });
        } catch (replyError) {
            client.logger.error('Failed to send error reply:', replyError);
        }
    }
}

export async function handleModalSubmission(interaction, client) {
    // Early checks
    if (!interaction.isModalSubmit()) return;

    // Log all available fields for debugging
    client.logger.error('Modal Submission Debug:', {
        customId: interaction.customId,
        fields: Array.from(interaction.fields.fields.keys())
    });

    const voiceChannel = interaction.member?.voice?.channel;
    if (!voiceChannel || !ChannelUtils.isTempChannel(voiceChannel.id)) {
        await interaction.deferReply({ flags: MessageFlags.Ephemeral });
        await interaction.editReply({
            content: '❌ You must be in a temporary voice channel to modify settings!',
        });
        return;
    }

    try {
        // Extract input values with comprehensive error handling
        let channelName = '', userLimit = 0, bitrate = 64, nsfwStatus = 'no';
        
        // Helper function to safely get text input value
        const safeGetTextInputValue = (customId) => {
            try {
                return interaction.fields.getTextInputValue(customId).trim();
            } catch (error) {
                client.logger.error(`Error getting input for ${customId}:`, error);
                return null;
            }
        };

        // Attempt to get values with fallback
        channelName = safeGetTextInputValue('name_input') || voiceChannel.name;
        const userLimitInput = safeGetTextInputValue('limit_input');
        const bitrateInput = safeGetTextInputValue('bitrate_input');
        const nsfwInput = safeGetTextInputValue('nsfw_input');

        // Parse user limit
        try {
            userLimit = userLimitInput ? parseInt(userLimitInput, 10) : voiceChannel.userLimit;
        } catch {
            userLimit = voiceChannel.userLimit;
        }

        // Parse bitrate
        try {
            bitrate = bitrateInput ? parseInt(bitrateInput, 10) : 64;
        } catch {
            bitrate = 64;
        }

        // Parse NSFW status
        if (nsfwInput) {
            nsfwStatus = ['yes', 'y', 'true', '1'].includes(nsfwInput.toLowerCase()) ? 'yes' : 'no';
        }

        // Validate inputs with fallback values
        channelName = channelName.length > 0 && channelName.length <= 32 
            ? channelName 
            : voiceChannel.name.slice(0, 32);

        userLimit = isNaN(userLimit) || userLimit < 0 || userLimit > 99 
            ? voiceChannel.userLimit 
            : userLimit;

        bitrate = isNaN(bitrate) || bitrate < 8 || bitrate > 384 
            ? 64 
            : bitrate;

        // Apply changes
        try {
            // Update channel name
            await voiceChannel.setName(channelName);

            // Update user limit
            await ChannelUtils.setUserLimit(voiceChannel, userLimit);

            // Update bitrate
            await ChannelUtils.setBitrate(voiceChannel, bitrate * 1000);

            // Update NSFW status
            const isNsfw = nsfwStatus === 'yes';
            await ChannelUtils.setNsfw(voiceChannel.id, voiceChannel, isNsfw, client);

            // Recreate control panel to reflect changes
            await createControlPanel(voiceChannel, client);

            // Confirm changes
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '✅ Channel settings updated successfully!',
                flags: MessageFlags.Ephemeral,
            });
        } catch (updateError) {
            client.logger.error('Failed to update channel settings:', updateError);
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ Failed to update channel settings. Check bot permissions.',
                flags: MessageFlags.Ephemeral,
            });
        }
    } catch (error) {
        client.logger.error('Settings modal submission error:', error);
        
        try {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            await interaction.editReply({
                content: '❌ An error occurred while processing your request.',
                flags: MessageFlags.Ephemeral,
            });
        } catch (replyError) {
            client.logger.error('Failed to send error reply:', replyError);
        }
    }
}
