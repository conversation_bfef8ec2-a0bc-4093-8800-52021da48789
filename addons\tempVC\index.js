import { ChannelType, PermissionFlagsBits, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, MessageFlags, ComponentType, TextDisplayBuilder, ContainerBuilder } from 'discord.js';
import { ChannelUtils } from './utils/channelUtils.js';
import { handleVCButtonInteraction, handleModalSubmission } from './events/buttonHandler.js';

let voiceStateListener = null;
let interactionListener = null;

// Add this function to create control panel buttons
async function createControlPanel(voiceChannel, client) {
    const tempChannelData = ChannelUtils.getTempChannel(voiceChannel.id);
    
    // Determine if the owner is in the channel
    const owner = voiceChannel.guild.members.cache.get(tempChannelData?.ownerId);
    const ownerInChannel = owner && voiceChannel.members.has(owner.id);

    // Calculate current channel quality
    const currentBitrate = voiceChannel.bitrate / 1000;
    const qualityIcon = currentBitrate >= 128 ? '🎧' : currentBitrate >= 96 ? '🔊' : '📻';
    const qualityText = `${currentBitrate}kbps ${qualityIcon}`;

    // Detect current channel activities
    const hasScreenShare = voiceChannel.members.some(member => member.voice.streaming);
    const hasCameras = voiceChannel.members.some(member => member.voice.selfVideo);

    // Prepare channel details
    const channelDetails = [
        `🎙️ **Channel:** \`${voiceChannel.name}\``,
        `👤 **Owner:** ${owner?.displayName || 'Undefined'}`,
        `⏰ **Created:** <t:${tempChannelData?.createdTimestamp || Math.floor(Date.now() / 1000)}:R>`,
        `👥 **Users:** ${voiceChannel.members.size}/${voiceChannel.userLimit || '∞'}`,
        `🎵 **Quality:** ${qualityText}`
    ];

    // Prepare activity status
    if (hasScreenShare || hasCameras) {
        const activities = [];
        if (hasScreenShare) activities.push('📺 Screen sharing');
        if (hasCameras) activities.push('📹 Camera');
        channelDetails.push(`🎬 **Active:** ${activities.join(', ')}`);
    }

    // Add NSFW status if applicable
    if (tempChannelData?.settings.nsfw) {
        channelDetails.push('🔞 **NSFW:** Enabled');
    }

    // Create text display
    const textDisplay = new TextDisplayBuilder()
        .setContent(channelDetails.join('\n') + '\n\n💡 **Tip:** Quality auto-adjusts for screen sharing & cameras');

    // First row: Channel Settings, Access Management, Voice Regions
    const row1 = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId('vc_settings')
            .setLabel('Channel Settings')
            .setStyle(ButtonStyle.Primary),
        new ButtonBuilder()
            .setCustomId('vc_access')
            .setLabel('Access Management')
            .setStyle(ButtonStyle.Secondary),
        new ButtonBuilder()
            .setCustomId('vc_regions')
            .setLabel('Voice Regions')
            .setStyle(ButtonStyle.Secondary)
    );

    // Second row: Lock/Unlock, Hide/Show
    const row2 = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId('vc_lock')
            .setLabel(tempChannelData?.settings.locked ? 'Unlock' : 'Lock Channel')
            .setStyle(tempChannelData?.settings.locked ? ButtonStyle.Success : ButtonStyle.Danger),
        new ButtonBuilder()
            .setCustomId('vc_hide')
            .setLabel(tempChannelData?.settings.hidden ? 'Show' : 'Hide Channel')
            .setStyle(tempChannelData?.settings.hidden ? ButtonStyle.Success : ButtonStyle.Secondary)
    );

    // Optional third row for Claim Channel
    let row3 = null;
    if (!ownerInChannel) {
        row3 = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setCustomId('vc_claim')
                .setLabel('👑 Claim Channel')
                .setStyle(ButtonStyle.Primary)
        );
    }

    // Fourth row: Transfer Ownership
    const row4 = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId('vc_transfer_ownership')
            .setLabel('👑 Transfer Ownership')
            .setStyle(ButtonStyle.Secondary)
    );

    // Combine rows, filtering out null rows
    const rows = [row1, row2];
    if (row3) rows.push(row3);
    rows.push(row4);

    // Create container
    const randomColor = Math.floor(Math.random() * 0xFFFFFF);
    const container = new ContainerBuilder()
        .setAccentColor(randomColor)
        .addTextDisplayComponents(textDisplay)
        .addActionRowComponents(row1.toJSON(), row2.toJSON());

    // Add claim row if it exists
    if (row3) {
        container.addActionRowComponents(row3.toJSON());
    }

    // Add transfer ownership row
    container.addActionRowComponents(row4.toJSON());

    // Try to update existing control panel or create a new one
    try {
        // Check if a control panel message already exists
        if (tempChannelData && tempChannelData.controlPanelMessageId) {
            try {
                // Try to fetch the existing message
                const existingMessage = await voiceChannel.messages.fetch(tempChannelData.controlPanelMessageId);
                
                // Update the existing message
                await existingMessage.edit({
                    components: [container.toJSON()],
                    flags: MessageFlags.IsComponentsV2
                });

                client.logger.info(`Updated existing control panel for channel ${voiceChannel.name}`);
                return existingMessage;
            } catch (fetchError) {
                // If message can't be fetched, fall back to creating a new one
                client.logger.warn(`Could not fetch existing control panel message: ${fetchError.message}`);
            }
        }

        // If no existing message or fetch failed, create a new message
        const message = await voiceChannel.send({
            components: [container.toJSON()],
            flags: MessageFlags.IsComponentsV2
        });

        // Update temp channel data with new message ID
        if (tempChannelData) {
            tempChannelData.controlPanelMessageId = message.id;
        }

        client.logger.info(`Created new control panel for channel ${voiceChannel.name}`);
        return message;
    } catch (error) {
        client.logger.error('Failed to send/update control panel:', error);
        return null;
    }
}

// Export the function if you want to use it elsewhere
export { createControlPanel };

export async function init(client, addonConfig) {
    console.log("[TEMP VC ADDON] Loaded!");

    const lobbyChannelId = addonConfig.lobbyChannel;
    const defaultChannelName = addonConfig.defaultChannelName || '🔊│';
    const defaultUserLimit = addonConfig.defaultUserLimit || 0;
    const embedColor = addonConfig.embedColor || 0x0099FF;

    // Load existing temp channels from database
    await ChannelUtils.loadPersistedTempChannels(client);
    await ChannelUtils.detectAndSyncExistingTempChannels(client, addonConfig);

    // Clean up empty temporary voice channels on bot restart
    const deletedChannelsCount = await ChannelUtils.cleanupEmptyTempChannels(client);
    console.log(`[TEMP VC ADDON] Cleaned up ${deletedChannelsCount} empty temporary voice channels.`);

    // Set up periodic validation of temporary voice channels
    const validationConfig = {
        validationIntervalMinutes: addonConfig.validationIntervalMinutes || 60, // Default 1 hour
        maxChannelAgeHours: addonConfig.maxChannelAgeHours || 24, // Default 24 hours
        maxIdleTimeHours: addonConfig.maxIdleTimeHours || 2 // Default 2 hours
    };
    ChannelUtils.setupPeriodicValidation(client, validationConfig);

    voiceStateListener = async (oldState, newState) => {
        try {
            const guild = newState.guild;
            const member = newState.member;

            // Check if user joined the lobby channel
            if (newState.channelId === lobbyChannelId && oldState.channelId !== lobbyChannelId) {
                const voiceChannel = await guild.channels.create({
                    name: `${defaultChannelName} ${member.user.username}`,
                    type: ChannelType.GuildVoice,
                    parent: guild.channels.cache.get(lobbyChannelId).parent,
                    userLimit: defaultUserLimit,
                    permissionOverwrites: [
                        {
                            id: member.id,
                            allow: [
                                PermissionFlagsBits.ManageChannels,
                                PermissionFlagsBits.MoveMembers
                            ]
                        },
                        {
                            id: guild.id,
                            allow: [
                                PermissionFlagsBits.Connect,
                                PermissionFlagsBits.Speak
                            ]
                        }
                    ]
                });

                // Move member to new channel
                await member.voice.setChannel(voiceChannel);

                // Add temp channel to tracking
                await ChannelUtils.addTempChannel(
                    voiceChannel.id, 
                    member.id, 
                    guild.id, 
                    client, 
                    addonConfig
                );

                // Create control panel
                await createControlPanel(voiceChannel, client);
            }

            // Handle ownership transfer when owner leaves
            if (oldState.channelId && ChannelUtils.isTempChannel(oldState.channelId)) {
                const tempChannel = oldState.channel;
                const tempChannelData = ChannelUtils.getTempChannel(tempChannel.id);

                // Check if the leaving member was the owner
                if (tempChannelData && tempChannelData.ownerId === oldState.member.id) {
                    // Find potential new owners (members in the channel)
                    const potentialOwners = tempChannel.members
                        .filter(member => 
                            member.id !== oldState.member.id && 
                            // Exclude bots from ownership
                            !member.user.bot
                        )
                        // Sort by join time, with preference to longer-standing members
                        .sort((a, b) => a.joinedTimestamp - b.joinedTimestamp);

                    if (potentialOwners.size > 0) {
                        // Select the first (longest-standing) non-bot member as the new owner
                        const newOwner = potentialOwners.first();

                        try {
                            // Transfer ownership
                            await ChannelUtils.claimChannel(tempChannel.id, newOwner.id, client);

                            // Update channel name to reflect new owner
                            const newChannelName = `${defaultChannelName} ${newOwner.user.username}`;
                            await tempChannel.setName(newChannelName);

                            // Log the automatic ownership transfer
                            client.logger.info(`Automatically transferred ownership of channel ${tempChannel.name} from ${oldState.member.displayName} to ${newOwner.displayName}`);

                            // Update the control panel
                            await createControlPanel(tempChannel, client);
                        } catch (transferError) {
                            client.logger.error('Failed to automatically transfer channel ownership:', transferError);
                        }
                    } else {
                        // No suitable owner found - this might happen if only bots are left
                        client.logger.warn(`No suitable owner found for channel ${tempChannel.name} after ${oldState.member.displayName} left`);
                    }
                }
            }

            // Handle channel cleanup when last member leaves
            if (oldState.channelId && oldState.channel.members.size === 0 && 
                ChannelUtils.isTempChannel(oldState.channelId)) {
                const tempChannel = oldState.channel;
                
                // Log the empty channel details before deletion
                client.logger.info(`Deleting empty temporary voice channel: ${tempChannel.name} (${tempChannel.id})`);
                
                // Remove from temp channels
                await ChannelUtils.removeTempChannel(tempChannel.id, client);
                
                // Delete the channel
                await tempChannel.delete('Temp channel empty');
            }
        } catch (error) {
            console.error("[TEMP VC ADDON] Voice state error:", error);
        }
    };

    interactionListener = async (interaction) => {
        // Check for button interactions
        if (interaction.isButton()) {
            await handleVCButtonInteraction(interaction, client);
        }
        // Check for select menu interactions
        else if (interaction.isUserSelectMenu() || interaction.isStringSelectMenu()) {
            await handleVCButtonInteraction(interaction, client);
        }
        // Check for modal submissions
        else if (interaction.isModalSubmit()) {
            await handleModalSubmission(interaction, client);
        }
    };

    client.on('voiceStateUpdate', voiceStateListener);
    client.on('interactionCreate', interactionListener);
}

export function cleanup(client) {
    // Clear periodic validation interval
    ChannelUtils.clearPeriodicValidation(client);

    // Remove voice state and interaction listeners
    if (voiceStateListener) {
        client.off('voiceStateUpdate', voiceStateListener);
        voiceStateListener = null;
    }

    if (interactionListener) {
        client.off('interactionCreate', interactionListener);
        interactionListener = null;
    }

    console.log("[TEMP VC ADDON] Cleaned up listeners and intervals.");
}
