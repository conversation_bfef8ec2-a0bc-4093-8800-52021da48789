import { PermissionFlagsBits, ChannelType, MessageFlags } from 'discord.js';
import fs from 'node:fs';
import path from 'node:path';
import yaml from 'yaml';

export class ChannelUtils {
    static tempChannels = new Map();
    static authorizedJoins = new Map();
    static kickProtection = new Map();

    static async addTempChannel(channelId, ownerId, guildId, bot, addonConfig) {
        // Get the channel to extract its name
        let channel = null;
        
        // Try multiple ways to get the channel
        if (bot && bot.client && bot.client.channels) {
            channel = bot.client.channels.cache.get(channelId);
        } else if (bot && bot.channels) {
            channel = bot.channels.cache.get(channelId);
        }

        const channelName = channel ? channel.name : `Temp Voice Channel ${ownerId.slice(-4)}`;

        const tempChannelData = {
            channelId,
            ownerId,
            guildId,
            channelName, // Add channel name to the data
            controlPanelMessageId: null, // Initialize control panel message ID
            settings: {
                locked: false,
                hidden: false,
                blocked: {
                    view: [],
                    join: [],
                },
                allowed: [],
                userLimit: channel?.userLimit || 0,
                bitrate: channel?.bitrate || 64000,
                nsfw: channel?.nsfw || false,
            },
            createdAt: new Date(),
            lastActivity: new Date(),
            createdTimestamp: Math.floor(Date.now() / 1000),
        };

        this.tempChannels.set(channelId, tempChannelData);

        try {
            if (bot && bot.db) {
                await bot.db.insertOne('temp_channels', {
                    ...tempChannelData,
                    createdAt: tempChannelData.createdAt.toISOString(),
                    lastActivity: tempChannelData.lastActivity.toISOString(),
                });
            }
        }
        catch (error) {
            // Only log if it's not a connection error
            if (!(error instanceof Error && error.name === 'MongoServerSelectionError')) {
                if (bot && bot.logger) {
                    bot.logger.error('Failed to persist temp channel to database:', error);
                } else {
                    console.error('Failed to persist temp channel to database:', error);
                }
            }
        }

        return tempChannelData;
    }

    static async removeTempChannel(channelId, bot) {
        this.tempChannels.delete(channelId);
        this.kickProtection.delete(channelId);

        try {
            if (bot && bot.db) {
                await bot.db.deleteOne('temp_channels', { channelId });
            }
        }
        catch (error) {
            // Only log if it's not a connection error
            if (!(error instanceof Error && error.name === 'MongoServerSelectionError')) {
                if (bot && bot.logger) {
                    bot.logger.error('Failed to remove temp channel from database:', error);
                } else {
                    console.error('Failed to remove temp channel from database:', error);
                }
            }
        }
    }

    static async loadPersistedTempChannels(bot) {
        try {
            if (!bot || !bot.db) {
                return;
            }

            const tempChannels = await bot.db.find('temp_channels', {});
            
            // Determine the client object
            let clientToUse = null;
            if (bot.client && bot.client.guilds) {
                clientToUse = bot.client;
            } else if (bot.guilds) {
                clientToUse = bot;
            }

            if (!clientToUse) {
                bot.logger.error('No valid client object found for loading persisted temp channels');
                return;
            }

            bot.logger.info(`Loading ${tempChannels.length} persisted temp channels from database`);
            
            for (const channelData of tempChannels) {
                try {
                    // Validate that the channel still exists in Discord
                    const guild = clientToUse.guilds.cache.get(channelData.guildId);
                    if (!guild) {
                        bot.logger.warn(`Guild ${channelData.guildId} not found for temp channel ${channelData.channelId}, removing from DB`);
                        await this.removeTempChannel(channelData.channelId, bot);
                        continue;
                    }

                    const channel = guild.channels.cache.get(channelData.channelId);
                    if (!channel) {
                        bot.logger.warn(`Temp channel ${channelData.channelId} no longer exists in Discord, removing from DB`);
                        await this.removeTempChannel(channelData.channelId, bot);
                        continue;
                    }

                    // Validate channel type
                    if (channel.type !== 2) { // 2 = GuildVoice
                        bot.logger.warn(`Channel ${channelData.channelId} is not a voice channel, removing from DB`);
                        await this.removeTempChannel(channelData.channelId, bot);
                        continue;
                    }

                    // Check if owner still exists
                    const owner = guild.members.cache.get(channelData.ownerId);
                    if (!owner) {
                        bot.logger.warn(`Owner ${channelData.ownerId} not found for temp channel ${channelData.channelId}, removing from DB`);
                        await this.removeTempChannel(channelData.channelId, bot);
                        continue;
                    }

                    const tempChannelData = {
                        ...channelData,
                        createdAt: new Date(channelData.createdAt),
                        lastActivity: new Date(channelData.lastActivity),
                        createdTimestamp: channelData.createdTimestamp || Math.floor(new Date(channelData.createdAt).getTime() / 1000),
                    };
                    
                    this.tempChannels.set(channelData.channelId, tempChannelData);
                    bot.logger.info(`Loaded temp channel: ${channel.name} (${channel.id}) owned by ${owner.displayName}`);
                } catch (channelError) {
                    bot.logger.error(`Error processing temp channel ${channelData.channelId}:`, channelError);
                    // Remove problematic channel from DB
                    try {
                        await this.removeTempChannel(channelData.channelId, bot);
                    } catch (removeError) {
                        bot.logger.error(`Failed to remove problematic temp channel ${channelData.channelId}:`, removeError);
                    }
                }
            }
            
            bot.logger.info(`Successfully loaded ${this.tempChannels.size} temp channels into memory`);
        }
        catch (error) {
            // Only log if it's not a connection error
            if (!(error instanceof Error && error.name === 'MongoServerSelectionError')) {
                if (bot && bot.logger) {
                    bot.logger.error('Failed to load persisted temp channels:', error);
                } else {
                    console.error('Failed to load persisted temp channels:', error);
                }
            }
        }
    }

    static async syncTempChannelsWithDatabase(bot) {
        try {
            if (!bot || !bot.db) {
                return;
            }

            bot.logger.info('Starting temp channels database sync...');
            
            // Get all channels from database
            const dbChannels = await bot.db.find('temp_channels', {});
            const dbChannelIds = new Set(dbChannels.map(c => c.channelId));
            const memoryChannelIds = new Set(this.tempChannels.keys());
            
            // Find channels in DB but not in memory (need to add)
            const channelsToAdd = Array.from(dbChannelIds).filter(id => !memoryChannelIds.has(id));
            
            // Find channels in memory but not in DB (need to remove)
            const channelsToRemove = Array.from(memoryChannelIds).filter(id => !dbChannelIds.has(id));
            
            // Add missing channels to memory
            for (const channelId of channelsToAdd) {
                const channelData = dbChannels.find(c => c.channelId === channelId);
                if (channelData) {
                    try {
                        const guild = bot.client.guilds.cache.get(channelData.guildId);
                        if (guild) {
                            const channel = guild.channels.cache.get(channelId);
                            if (channel && channel.type === 2) { // GuildVoice
                                const tempChannelData = {
                                    ...channelData,
                                    createdAt: new Date(channelData.createdAt),
                                    lastActivity: new Date(channelData.lastActivity),
                                    createdTimestamp: channelData.createdTimestamp || Math.floor(new Date(channelData.createdAt).getTime() / 1000),
                                };
                                this.tempChannels.set(channelId, tempChannelData);
                                bot.logger.info(`Synced missing temp channel: ${channel.name} (${channelId})`);
                            }
                        }
                    } catch (error) {
                        bot.logger.error(`Error syncing channel ${channelId}:`, error);
                    }
                }
            }
            
            // Remove stale channels from memory
            for (const channelId of channelsToRemove) {
                this.tempChannels.delete(channelId);
                bot.logger.info(`Removed stale temp channel from memory: ${channelId}`);
            }
            
            bot.logger.info(`Database sync complete. Memory: ${this.tempChannels.size}, Database: ${dbChannels.length}`);
            
        } catch (error) {
            if (!(error instanceof Error && error.name === 'MongoServerSelectionError')) {
                bot.logger.error('Failed to sync temp channels with database:', error);
            }
        }
    }

    static async detectAndSyncExistingTempChannels(bot, addonConfig) {
        try {
            if (!bot || !bot.client) {
                return;
            }

            bot.logger.info('Starting detection of existing temp voice channels...');
            let detectedCount = 0;

            // Get all guilds the bot is in
            for (const guild of bot.client.guilds.cache.values()) {
                // Look for voice channels that match temp VC naming pattern
                const voiceChannels = guild.channels.cache.filter(channel => 
                    channel.type === 2 && // GuildVoice
                    channel.name.includes(addonConfig.defaultChannelName) &&
                    !this.tempChannels.has(channel.id) &&
                    channel.members.size > 0 // Only channels with people in them
                );

                for (const [channelId, channel] of voiceChannels) {
                    try {
                        // Try to find the owner (first person who joined or has manage permissions)
                        let owner = null;
                        
                        // First, check if any member has manage channels permission
                        for (const [memberId, member] of channel.members) {
                            if (member.permissions.has(PermissionFlagsBits.ManageChannels)) {
                                owner = member;
                                break;
                            }
                        }
                        
                        // If no owner found, use the first member
                        if (!owner && channel.members.size > 0) {
                            owner = channel.members.first();
                        }

                        if (owner) {
                            // Create temp channel data for this existing channel
                            const tempChannelData = {
                                channelId: channel.id,
                                ownerId: owner.id,
                                guildId: guild.id,
                                settings: {
                                    locked: false,
                                    hidden: false,
                                    blocked: { view: [], join: [] },
                                    allowed: [],
                                    userLimit: channel.userLimit || 0,
                                    bitrate: channel.bitrate || 64000,
                                    nsfw: channel.nsfw || false,
                                },
                                createdAt: new Date(),
                                lastActivity: new Date(),
                                createdTimestamp: Math.floor(Date.now() / 1000),
                            };

                            // Add to memory
                            this.tempChannels.set(channelId, tempChannelData);
                            
                            // Add to database
                            try {
                                if (bot.db) {
                                    await bot.db.insertOne('temp_channels', {
                                        ...tempChannelData,
                                        createdAt: tempChannelData.createdAt.toISOString(),
                                        lastActivity: tempChannelData.lastActivity.toISOString(),
                                    });
                                }
                            } catch (dbError) {
                                bot.logger.warn(`Failed to persist detected temp channel ${channelId} to database:`, dbError);
                            }

                            // Don't create a new control panel - just track the channel
                            // The main system will handle control panel creation/editing
                            bot.logger.info(`Detected existing temp channel: ${channel.name} (${channelId}) owned by ${owner.displayName} - no control panel created`);

                            detectedCount++;
                            bot.logger.info(`Detected and synced existing temp channel: ${channel.name} (${channelId}) owned by ${owner.displayName}`);
                        }
                    } catch (error) {
                        bot.logger.error(`Error processing detected channel ${channelId}:`, error);
                    }
                }
            }

            bot.logger.info(`Detection complete. Found and synced ${detectedCount} existing temp channels`);
            
        } catch (error) {
            bot.logger.error('Failed to detect existing temp channels:', error);
        }
    }

    static async validateAndRepairControlPanels(bot) {
        try {
            if (!bot || !bot.client) {
                return;
            }

            bot.logger.info('Starting control panel validation and repair...');
            let repairedCount = 0;
            let removedCount = 0;

            for (const [channelId, tempChannelData] of this.tempChannels.entries()) {
                try {
                    const guild = bot.client.guilds.cache.get(tempChannelData.guildId);
                    if (!guild) {
                        bot.logger.warn(`Guild ${tempChannelData.guildId} not found for temp channel ${channelId}, removing`);
                        await this.removeTempChannel(channelId, bot);
                        removedCount++;
                        continue;
                    }

                    const channel = guild.channels.cache.get(channelId);
                    if (!channel || channel.type !== 2) {
                        bot.logger.warn(`Temp channel ${channelId} no longer exists or is not a voice channel, removing`);
                        await this.removeTempChannel(channelId, bot);
                        removedCount++;
                        continue;
                    }

                    // Check if control panel message exists and is valid
                    if (tempChannelData.controlPanelMessageId) {
                        try {
                            const message = await channel.messages.fetch(tempChannelData.controlPanelMessageId);
                            if (!message) {
                                bot.logger.warn(`Control panel message ${tempChannelData.controlPanelMessageId} not found for channel ${channelId}, will recreate`);
                                tempChannelData.controlPanelMessageId = null;
                            } else {
                                // Ensure the message is still in the correct channel
                                if (message.channel.id !== channelId) {
                                    bot.logger.warn(`Control panel message ${tempChannelData.controlPanelMessageId} found in wrong channel (${message.channel.id}) for channel ${channelId}, will recreate`);
                                    tempChannelData.controlPanelMessageId = null;
                                } else {
                                    bot.logger.debug(`Control panel validated for channel ${channel.name}`);
                                    repairedCount++;
                                }
                            }
                        } catch (messageError) {
                            if (messageError.code === 10008) { // Unknown Message
                                bot.logger.warn(`Control panel message ${tempChannelData.controlPanelMessageId} not found for channel ${channelId}, will recreate`);
                                tempChannelData.controlPanelMessageId = null;
                            } else {
                                bot.logger.error(`Error checking control panel message for channel ${channelId}:`, messageError);
                            }
                        }
                    }

                    // If no control panel message, mark it for repair but don't create a new one
                    if (!tempChannelData.controlPanelMessageId) {
                        bot.logger.info(`Channel ${channelId} has no control panel - this is normal for existing channels on restart`);
                        // Don't create a new message here - let the main system handle it when needed
                        // This prevents duplicate messages and ensures proper control panel management
                    }

                } catch (error) {
                    bot.logger.error(`Error validating temp channel ${channelId}:`, error);
                }
            }

            bot.logger.info(`Control panel validation complete. Repaired: ${repairedCount}, Removed: ${removedCount}`);
            
        } catch (error) {
            bot.logger.error('Failed to validate and repair control panels:', error);
        }
    }

    static getTempChannelHealthStatus(bot) {
        const status = {
            memoryChannels: this.tempChannels.size,
            databaseChannels: 0,
            validChannels: 0,
            invalidChannels: 0,
            channelsWithControlPanels: 0,
            channelsWithoutControlPanels: 0,
            details: []
        };

        try {
            if (bot && bot.db) {
                bot.db.find('temp_channels', {}).then(channels => {
                    status.databaseChannels = channels.length;
                }).catch(() => {
                    status.databaseChannels = 'Error';
                });
            }

            for (const [channelId, tempChannelData] of this.tempChannels.entries()) {
                try {
                    const guild = bot.client.guilds.cache.get(tempChannelData.guildId);
                    const channel = guild?.channels.cache.get(channelId);
                    
                    const channelStatus = {
                        channelId,
                        channelName: channel?.name || 'Unknown',
                        ownerId: tempChannelData.ownerId,
                        hasControlPanel: !!tempChannelData.controlPanelMessageId,
                        isValid: !!(guild && channel && channel.type === 2),
                        guildExists: !!guild,
                        channelExists: !!channel,
                        isVoiceChannel: channel?.type === 2
                    };

                    if (channelStatus.isValid) {
                        status.validChannels++;
                    } else {
                        status.invalidChannels++;
                    }

                    if (channelStatus.hasControlPanel) {
                        status.channelsWithControlPanels++;
                    } else {
                        status.channelsWithoutControlPanels++;
                    }

                    status.details.push(channelStatus);
                } catch (error) {
                    status.details.push({
                        channelId,
                        channelName: 'Error',
                        ownerId: tempChannelData.ownerId,
                        hasControlPanel: !!tempChannelData.controlPanelMessageId,
                        isValid: false,
                        memberCount: 0,
                        error: error.message
                    });
                    status.invalidChannels++;
                }
            }
        } catch (error) {
            status.error = error.message;
        }

        return status;
    }

    static getTempChannel(channelId) {
        return this.tempChannels.get(channelId);
    }

    static getTempChannels() {
        return Array.from(this.tempChannels.values());
    }

    static isTempChannel(channelId) {
        return this.tempChannels.has(channelId);
    }

    static async lockChannel(channelId) {
        const tempChannelData = this.tempChannels.get(channelId);
        if (tempChannelData) {
            tempChannelData.settings.locked = true;
            tempChannelData.lastActivity = new Date();
        }
    }

    static async unlockChannel(channelId) {
        const tempChannelData = this.tempChannels.get(channelId);
        if (tempChannelData) {
            tempChannelData.settings.locked = false;
            tempChannelData.lastActivity = new Date();
        }
    }

    static async hideChannel(channelId, channel, bot) {
        const tempChannelData = this.tempChannels.get(channelId);
        if (tempChannelData) {
            tempChannelData.settings.hidden = true;
            tempChannelData.lastActivity = new Date();
        }

        try {
            await channel.permissionOverwrites.edit(channel.guild.roles.everyone, {
                ViewChannel: false,
                Connect: false,
                Speak: false,
                Stream: false,
                UseVAD: false,
                UseSoundboard: false,
                RequestToSpeak: false,
            }, 'Temp Voice - Channel hidden');
        }
        catch (error) {
            if (bot && bot.logger) {
                bot.logger.error('Failed to hide channel:', error);
            } else {
                console.error('Failed to hide channel:', error);
            }
        }
    }

    static async showChannel(channelId, channel, bot) {
        const tempChannelData = this.tempChannels.get(channelId);
        if (tempChannelData) {
            tempChannelData.settings.hidden = false;
            tempChannelData.lastActivity = new Date();
        }

        try {
            await channel.permissionOverwrites.edit(channel.guild.roles.everyone, {
                ViewChannel: true,
                Connect: true,
                Speak: true,
                Stream: true,
                UseVAD: true,
                UseSoundboard: true,
                RequestToSpeak: true,
            }, 'Temp Voice - Channel shown');
        }
        catch (error) {
            if (bot && bot.logger) {
                bot.logger.error('Failed to show channel:', error);
            } else {
                console.error('Failed to show channel:', error);
            }
        }
    }

    static async setUserLimit(channel, limit) {
        await channel.setUserLimit(limit);
    }

    static async setBitrate(channel, bitrate) {
        await channel.setBitrate(bitrate);
    }

    static async setNsfw(channelId, channel, nsfw, bot) {
        const tempChannelData = this.tempChannels.get(channelId);
        if (tempChannelData) {
            tempChannelData.settings.nsfw = nsfw;
            tempChannelData.lastActivity = new Date();
        }

        try {
            await channel.edit({
                nsfw: nsfw
            });

            // Update database to persist the change
            if (bot && bot.db) {
                await bot.db.updateOne('temp_channels',
                    { channelId: channelId },
                    { $set: { 'settings.nsfw': nsfw, lastActivity: new Date().toISOString() } }
                );
            }
        }
        catch (error) {
            if (bot && bot.logger) {
                bot.logger.error('Failed to set NSFW status:', error);
            } else {
                console.error('Failed to set NSFW status:', error);
            }
        }
    }

    // Quality monitoring and adaptive adjustment
    static async monitorChannelQuality(channelId, bot) {
        const tempChannelData = this.tempChannels.get(channelId);
        if (!tempChannelData) return;

        const channel = bot.client.channels.cache.get(channelId);
        if (!channel || channel.type !== 2) return; // 2 = GuildVoice

        try {
            // Check for screen sharing and camera usage
            const membersWithVideo = channel.members.filter(member => 
                member.voice.streaming || member.voice.selfVideo
            );

            const hasScreenShare = channel.members.some(member => member.voice.streaming);
            const hasCamera = channel.members.some(member => member.voice.selfVideo);
            const memberCount = channel.members.size;

            // Determine optimal quality settings
            const qualitySettings = this.calculateOptimalQuality({
                hasScreenShare,
                hasCamera,
                memberCount,
                currentBitrate: channel.bitrate
            });

            // Apply quality adjustments if needed
            if (qualitySettings.shouldAdjust) {
                await this.applyQualitySettings(channel, qualitySettings, bot);
                
                // Update temp channel data
                tempChannelData.settings.adaptiveQuality = {
                    lastAdjustment: new Date(),
                    reason: qualitySettings.reason,
                    previousBitrate: channel.bitrate,
                    currentBitrate: qualitySettings.bitrate
                };

                // Log quality adjustment
                if (bot.logger) {
                    bot.logger.info(`Quality adjusted for channel ${channel.name}: ${qualitySettings.previousBitrate/1000}k → ${qualitySettings.bitrate/1000}k)`);
                }
            }

            // Store current activity state
            tempChannelData.settings.currentActivity = {
                screenSharing: hasScreenShare,
                cameraUsage: hasCamera,
                videoUsers: membersWithVideo.size,
                lastCheck: new Date()
            };

        } catch (error) {
            if (bot.logger) {
                bot.logger.error(`Failed to monitor quality for channel ${channelId}:`, error);
            }
        }
    }

    static calculateOptimalQuality(conditions) {
        const { hasScreenShare, hasCamera, memberCount, currentBitrate } = conditions;
        
        let targetBitrate = currentBitrate;
        let shouldAdjust = false;
        let reason = '';

        // Screen sharing requires higher bitrate for quality
        if (hasScreenShare) {
            if (currentBitrate < 128000) {
                targetBitrate = Math.min(128000, 64000); // Boost to 128k if possible
                shouldAdjust = true;
                reason = 'Screen sharing detected - boosting quality';
            }
        }

        // Camera usage with many users - reduce quality to prevent lag
        if (hasCamera && memberCount > 5) {
            if (currentBitrate > 96000) {
                targetBitrate = Math.max(96000, 64000); // Reduce to 96k max
                shouldAdjust = true;
                reason = 'Camera usage with many users - optimizing for performance';
            }
        }

        // High member count without video - can reduce quality
        if (!hasCamera && !hasScreenShare && memberCount > 8) {
            if (currentBitrate > 64000) {
                targetBitrate = 64000; // Reduce to 64k for large groups
                shouldAdjust = true;
                reason = 'Large group without video - optimizing bandwidth';
            }
        }

        // Low member count with high quality - can boost if needed
        if (memberCount <= 3 && !hasScreenShare && currentBitrate < 128000) {
            targetBitrate = Math.min(128000, 64000); // Boost to 128k if possible
            shouldAdjust = true;
            reason = 'Small group - boosting quality for better experience';
        }

        return {
            shouldAdjust,
            bitrate: targetBitrate,
            reason,
            previousBitrate: currentBitrate
        };
    }

    static async applyQualitySettings(channel, qualitySettings, bot) {
        try {
            await channel.edit({
                bitrate: qualitySettings.bitrate
            });

            // Update the channel's bitrate in our data
            const tempChannelData = this.tempChannels.get(channel.id);
            if (tempChannelData) {
                tempChannelData.settings.bitrate = qualitySettings.bitrate;
            }

        } catch (error) {
            if (bot && bot.logger) {
                bot.logger.error(`Failed to apply quality settings to channel ${channel.id}:`, error);
            }
        }
    }

    // Auto-restore quality when conditions improve
    static async restoreOptimalQuality(channelId, bot, addonConfig) {
        const tempChannelData = this.tempChannels.get(channelId);
        if (!tempChannelData?.settings.adaptiveQuality) return;

        const channel = bot.client.channels.cache.get(channelId);
        if (!channel || channel.type !== 2) return;

        try {
            const lastAdjustment = tempChannelData.settings.adaptiveQuality.lastAdjustment;
            const timeSinceAdjustment = Date.now() - lastAdjustment.getTime();
            
            // Only restore after 5 minutes of stable conditions
            if (timeSinceAdjustment < 5 * 60 * 1000) return;

            const currentActivity = tempChannelData.settings.currentActivity;
            if (!currentActivity) return;

            // Check if conditions have improved
            const hasScreenShare = channel.members.some(member => member.voice.streaming);
            const hasCamera = channel.members.some(member => member.voice.selfVideo);
            const memberCount = channel.members.size;

            // If no more screen sharing/camera and smaller group, restore higher quality
            if (!hasScreenShare && !hasCamera && memberCount <= 5) {
                const originalBitrate = tempChannelData.settings.adaptiveQuality.previousBitrate;
                if (originalBitrate > channel.bitrate) {
                    await this.applyQualitySettings(channel, {
                        shouldAdjust: true,
                        bitrate: originalBitrate,
                        reason: 'Conditions improved - restoring original quality',
                        previousBitrate: channel.bitrate
                    }, bot);

                    // Clear adaptive quality data
                    delete tempChannelData.settings.adaptiveQuality;
                    
                    if (bot.logger) {
                        bot.logger.info(`Quality restored for channel ${channel.name}: ${channel.bitrate/1000}k → ${originalBitrate/1000}k`);
                    }
                }
            }

        } catch (error) {
            if (bot.logger) {
                bot.logger.error(`Failed to restore quality for channel ${channelId}:`, error);
            }
        }
    }

    // Device detection utility
    static isMobileUser(userAgent) {
        if (!userAgent) return false;
        
        const mobileKeywords = [
            'mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 
            'windows phone', 'opera mini', 'mobile safari', 'mobile chrome',
            'tablet', 'kindle', 'nook', 'playbook', 'webos', 'bada'
        ];
        
        const lowerUserAgent = userAgent.toLowerCase();
        return mobileKeywords.some(keyword => lowerUserAgent.includes(keyword));
    }

    // Get button labels based on device type
    static getButtonLabels(isMobile) {
        if (isMobile) {
            return {
                settings: '⚙️ Settings',
                access: '👥 Access',
                regions: '🌍 Regions',
                lock: '🔒 Lock',
                unlock: '🔓 Unlock',
                hide: '🫥 Hide',
                show: '👁️ Show',
                kick: '🦵 Kick',
                block: '🚫 Block',
                unblock: '✅ Unblock',
                manualId: '📝 Manual ID'
            };
        } else {
            return {
                settings: '⚙️ Channel Settings',
                access: '👥 Access Management',
                regions: '🌍 Voice Regions',
                lock: '🔒 Lock Channel',
                unlock: '🔓 Unlock Channel',
                hide: '🫥 Hide Channel',
                show: '👁️ Show Channel',
                kick: '🦵 Kick User',
                block: '🚫 Block User',
                unblock: '✅ Unblock User',
                manualId: '📝 Enter Manual ID'
            };
        }
    }

    // Claim an abandoned temporary voice channel
    static async claimChannel(channelId, newOwnerId, client) {
        const tempChannelData = this.tempChannels.get(channelId);
        if (!tempChannelData) {
            throw new Error('Channel not found in temp channels');
        }

        // Find the actual channel object
        const guild = client.guilds.cache.get(tempChannelData.guildId);
        if (!guild) {
            throw new Error('Guild not found');
        }

        const channel = guild.channels.cache.get(channelId);
        if (!channel) {
            throw new Error('Channel not found');
        }

        // Check if the new owner is in the channel
        const newOwnerMember = guild.members.cache.get(newOwnerId);
        if (!newOwnerMember || !channel.members.has(newOwnerId)) {
            throw new Error('New owner must be in the channel to claim it');
        }

        // Store the old owner ID for logging and potential permission reset
        const oldOwnerId = tempChannelData.ownerId;

        try {
            // Update channel permissions
            await channel.permissionOverwrites.edit(newOwnerId, {
                ManageChannels: true,
                MoveMembers: true,
                Connect: true,
                ViewChannel: true
            });

            // Remove special permissions from the old owner
            if (oldOwnerId !== newOwnerId) {
                try {
                    await channel.permissionOverwrites.edit(oldOwnerId, {
                        ManageChannels: null,
                        MoveMembers: null
                    });
                } catch (permError) {
                    // Log permission reset error, but don't block the claim process
                    client.logger.warn(`Failed to reset permissions for old owner ${oldOwnerId}:`, permError);
                }
            }

            // Update temporary channel data
            tempChannelData.ownerId = newOwnerId;
            tempChannelData.lastActivity = new Date();

            // Update database if available
            if (client && client.db) {
                try {
                    await client.db.updateOne('temp_channels', 
                        { channelId: channelId }, 
                        { 
                            $set: { 
                                ownerId: newOwnerId, 
                                lastActivity: new Date().toISOString() 
                            } 
                        }
                    );
                } catch (dbError) {
                    client.logger.error('Failed to update database for channel claim:', dbError);
                }
            }

            // Log the ownership change
            client.logger.info(`Channel ${channel.name} (${channelId}) ownership transferred from ${oldOwnerId} to ${newOwnerId}`);

            return tempChannelData;
        } catch (error) {
            // Revert ownership change if permission update fails
            tempChannelData.ownerId = oldOwnerId;
            throw new Error(`Failed to claim channel: ${error.message}`);
        }
    }

    // Check if a channel can be claimed (owner left)
    static canChannelBeClaimed(channelId, bot) {
        const tempChannelData = this.tempChannels.get(channelId);
        if (!tempChannelData) return false;

        const channel = bot.client.channels.cache.get(channelId);
        if (!channel) return false;

        // Check if owner is still in the channel
        return !channel.members.has(tempChannelData.ownerId);
    }

    static blockUser(channelId, userId, type) {
        const tempChannelData = this.tempChannels.get(channelId);
        if (tempChannelData && !tempChannelData.settings.blocked[type].includes(userId)) {
            tempChannelData.settings.blocked[type].push(userId);
            tempChannelData.lastActivity = new Date();
        }
    }

    static unblockUser(channelId, userId, type) {
        const tempChannelData = this.tempChannels.get(channelId);
        if (tempChannelData) {
            tempChannelData.settings.blocked[type] = tempChannelData.settings.blocked[type].filter(id => id !== userId);
            tempChannelData.lastActivity = new Date();
        }
    }

    static getBlockedUsers(channelId) {
        const tempChannelData = this.tempChannels.get(channelId);
        return tempChannelData ? tempChannelData.settings.blocked : { view: [], join: [] };
    }

    static addKickProtection(channelId, userId) {
        if (!this.kickProtection.has(channelId)) {
            this.kickProtection.set(channelId, new Set());
        }
        this.kickProtection.get(channelId).add(userId);
    }

    static hasKickProtection(channelId, userId) {
        const protectionSet = this.kickProtection.get(channelId);
        return protectionSet ? protectionSet.has(userId) : false;
    }

    static authorizeJoin(channelId, userId) {
        if (!this.authorizedJoins.has(channelId)) {
            this.authorizedJoins.set(channelId, new Set());
        }

        this.authorizedJoins.get(channelId).add(userId);
    }

    static isJoinAuthorized(channelId, userId) {
        const userSet = this.authorizedJoins.get(channelId);
        if (userSet && userSet.has(userId)) {
            userSet.delete(userId);
            if (userSet.size === 0) {
                this.authorizedJoins.delete(channelId);
            }
            return true;
        }

        return false;
    }

    static async updateTempChannelControlPanel(channelId, controlPanelMessageId, bot) {
        const tempChannelData = this.tempChannels.get(channelId);
        if (tempChannelData) {
            tempChannelData.controlPanelMessageId = controlPanelMessageId;
            tempChannelData.lastActivity = new Date();

            // Update database if available
            if (bot && bot.db) {
                try {
                    await bot.db.updateOne('temp_channels', 
                        { channelId: channelId }, 
                        { 
                            $set: { 
                                controlPanelMessageId: controlPanelMessageId,
                                lastActivity: new Date().toISOString() 
                            } 
                        }
                    );
                } catch (error) {
                    if (bot.logger) {
                        bot.logger.error('Failed to update control panel message ID in database:', error);
                    }
                }
            }
        }
    }

    // Clean up empty temporary voice channels
    static async cleanupEmptyTempChannels(client) {
        try {
            client.logger.info('Starting cleanup of empty temporary voice channels...');
            let deletedChannelsCount = 0;

            // Iterate through all known temporary channels
            for (const [channelId, tempChannelData] of this.tempChannels.entries()) {
                try {
                    // Find the actual channel object
                    const guild = client.guilds.cache.get(tempChannelData.guildId);
                    if (!guild) {
                        client.logger.warn(`Guild ${tempChannelData.guildId} not found for temp channel ${channelId}, skipping`);
                        continue;
                    }

                    const channel = guild.channels.cache.get(channelId);
                    
                    // Check if channel exists and is a voice channel
                    if (!channel || channel.type !== 2) { // 2 = GuildVoice
                        // Remove from temp channels if channel no longer exists
                        await this.removeTempChannel(channelId, client);
                        continue;
                    }

                    // Check if channel is empty
                    if (channel.members.size === 0) {
                        try {
                            // Delete the channel
                            await channel.delete('Temporary voice channel empty');
                            
                            // Remove from temp channels
                            await this.removeTempChannel(channelId, client);
                            
                            deletedChannelsCount++;
                            client.logger.info(`Deleted empty temporary voice channel: ${channel.name} (${channelId})`);
                        } catch (deleteError) {
                            client.logger.error(`Failed to delete empty temp channel ${channelId}:`, {
                                errorName: deleteError.name,
                                errorMessage: deleteError.message,
                                errorStack: deleteError.stack
                            });
                        }
                    }
                } catch (channelError) {
                    client.logger.error(`Error processing temp channel ${channelId} during cleanup:`, {
                        errorName: channelError.name,
                        errorMessage: channelError.message,
                        errorStack: channelError.stack
                    });
                }
            }

            client.logger.info(`Cleanup complete. Deleted ${deletedChannelsCount} empty temporary voice channels.`);
            return deletedChannelsCount;
        } catch (error) {
            client.logger.error('Failed to clean up empty temporary voice channels:', {
                errorName: error.name,
                errorMessage: error.message,
                errorStack: error.stack
            });
            return 0;
        }
    }

    // Periodic validation of temporary voice channels
    static async validateTempChannels(client, config) {
        try {
            client.logger.info('Starting periodic validation of temporary voice channels...');
            
            const validationStart = Date.now();
            let validChannelsCount = 0;
            let invalidChannelsCount = 0;
            let deletedChannelsCount = 0;

            // Define validation criteria
            const MAX_CHANNEL_AGE_HOURS = config?.maxChannelAgeHours || 24; // Default 24 hours
            const MAX_IDLE_TIME_HOURS = config?.maxIdleTimeHours || 2; // Default 2 hours

            // Iterate through all known temporary channels
            for (const [channelId, tempChannelData] of this.tempChannels.entries()) {
                try {
                    // Find the actual channel object
                    const guild = client.guilds.cache.get(tempChannelData.guildId);
                    if (!guild) {
                        client.logger.warn(`Guild ${tempChannelData.guildId} not found for temp channel ${channelId}, skipping`);
                        continue;
                    }

                    const channel = guild.channels.cache.get(channelId);
                    
                    // Check if channel exists and is a voice channel
                    if (!channel || channel.type !== 2) { // 2 = GuildVoice
                        await this.removeTempChannel(channelId, client);
                        invalidChannelsCount++;
                        continue;
                    }

                    // Calculate channel age
                    const channelAge = (Date.now() - tempChannelData.createdAt.getTime()) / (1000 * 60 * 60);
                    
                    // Calculate last activity time
                    const lastActivityAge = tempChannelData.lastActivity 
                        ? (Date.now() - tempChannelData.lastActivity.getTime()) / (1000 * 60 * 60) 
                        : channelAge;

                    // Check if channel should be deleted
                    const shouldDelete = 
                        channelAge > MAX_CHANNEL_AGE_HOURS || // Channel is too old
                        (channel.members.size === 0 && lastActivityAge > MAX_IDLE_TIME_HOURS); // Empty channel is idle too long

                    if (shouldDelete) {
                        try {
                            // Delete the channel
                            await channel.delete('Temporary voice channel expired');
                            
                            // Remove from temp channels
                            await this.removeTempChannel(channelId, client);
                            
                            deletedChannelsCount++;
                            client.logger.info(`Deleted expired temporary voice channel: ${channel.name} (${channelId})`);
                        } catch (deleteError) {
                            client.logger.error(`Failed to delete expired temp channel ${channelId}:`, {
                                errorName: deleteError.name,
                                errorMessage: deleteError.message,
                                errorStack: deleteError.stack
                            });
                        }
                    } else {
                        validChannelsCount++;
                    }
                } catch (channelError) {
                    client.logger.error(`Error processing temp channel ${channelId} during validation:`, {
                        errorName: channelError.name,
                        errorMessage: channelError.message,
                        errorStack: channelError.stack
                    });
                    invalidChannelsCount++;
                }
            }

            const validationDuration = Date.now() - validationStart;

            client.logger.info(`Periodic validation complete:`, {
                duration: `${validationDuration}ms`,
                totalChannels: this.tempChannels.size,
                validChannels: validChannelsCount,
                invalidChannels: invalidChannelsCount,
                deletedChannels: deletedChannelsCount
            });

            return {
                validChannels: validChannelsCount,
                invalidChannels: invalidChannelsCount,
                deletedChannels: deletedChannelsCount,
                duration: validationDuration
            };
        } catch (error) {
            client.logger.error('Failed to perform periodic validation of temporary voice channels:', {
                errorName: error.name,
                errorMessage: error.message,
                errorStack: error.stack
            });
            return {
                validChannels: 0,
                invalidChannels: 0,
                deletedChannels: 0,
                duration: 0
            };
        }
    }

    // Set up periodic validation interval
    static setupPeriodicValidation(client, config) {
        // Default to 1 hour if not specified
        const validationInterval = config?.validationIntervalMinutes || 60;
        const intervalMs = validationInterval * 60 * 1000;

        // Clear any existing interval to prevent duplicates
        if (client.tempVoiceValidationInterval) {
            clearInterval(client.tempVoiceValidationInterval);
        }

        // Set up new periodic validation interval
        client.tempVoiceValidationInterval = setInterval(async () => {
            try {
                await this.validateTempChannels(client, config);
            } catch (error) {
                client.logger.error('Error during periodic temp voice channel validation:', error);
            }
        }, intervalMs);

        client.logger.info(`Periodic temp voice channel validation set up. Interval: ${validationInterval} minutes`);

        // Return the interval ID in case it needs to be cleared later
        return client.tempVoiceValidationInterval;
    }

    // Cleanup method to remove interval when addon is unloaded
    static clearPeriodicValidation(client) {
        if (client.tempVoiceValidationInterval) {
            clearInterval(client.tempVoiceValidationInterval);
            delete client.tempVoiceValidationInterval;
            client.logger.info('Periodic temp voice channel validation interval cleared.');
        }
    }
}
