addon:
  name: "welcome-goodbye"
  version: "1.0.0"
  description: "👋 Welcome and farewell messages for new and leaving members"
  author: "OnedEyePete"
  enabled: true

settings:
  welcomeChannelId: "1384735024523513906" # Same as server-hub's hubChannelId
  goodbyeChannelId: "1384735024523513907" # New channel ID for goodbye messages
  
  hubContent:
    welcome:
      title: "Welcome to 404 Utopia! 🎮"
      subtitle: "🔞 Adult Gaming Community - 18+ Only 🔞"
      description: "Welcome to our **chill adult gaming hangout!** We're here to make friends, form new relationships, and have a great time together. Whether you're into **SFW fun** or **NSFW content**, we've got something for everyone!"
      accentColor: "9900FF"
      callToAction: "Click the buttons below to get started in our adult gaming community. Remember, you must be **18+** to participate!"
      footer: "**~ Welcome to 404 Utopia - Where a broken heaven! ~**"
    
    goodbye:
      title: "😢 Member Left"
      subtitle: "We're sad to see you go..."
      description: "A member has left our community. We hope they had a great time with us and will consider returning in the future!"
      accentColor: "FF0000"
      footer: "**~ 404 Utopia - Come back anytime! ~**"

features:
  welcome:
    enabled: true
    maintenanceMessage: "🔧 **Welcome System Under Maintenance**\nThe welcome system is currently being updated. Please check back later!"
  goodbye:
    enabled: true
    maintenanceMessage: "🔧 **Goodbye System Under Maintenance**\nThe goodbye system is currently being updated. Please check back later!"

database:
  collections:
    - "welcome_goodbye_logs"

logging:
  enabled: true
  logLevel: "info"
