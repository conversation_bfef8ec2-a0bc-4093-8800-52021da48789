import fs from 'node:fs';
import path from 'node:path';
import YAML from 'js-yaml';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { logger } from '../utils/logger.js';
import { createWelcomeMessage, createGoodbyeMessage } from './message-builder.js';

// Get __dirname equivalent in ES module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load configuration
let config;
try {
  const configPath = join(__dirname, 'config.yml');
  const configFile = fs.readFileSync(configPath, 'utf8');
  config = YAML.load(configFile);
  
  // Ensure features object exists
  if (!config.features) config.features = {};
  if (!config.features.welcome) config.features.welcome = {};
  if (!config.features.goodbye) config.features.goodbye = {};
} catch (error) {
  logger.error('Failed to load welcome-goodbye config:', { error: error.message });
  config = {
    features: {
      welcome: {},
      goodbye: {}
    }
  };
}

// Store references to components
const welcomeGoodbye = {
  welcomeChannelId: config.settings?.welcomeChannelId || '',
  goodbyeChannelId: config.settings?.goodbyeChannelId || '',
  logger: logger,
  config: config
};

// Event handlers
const events = {
  guildMemberAdd: async (member) => {
    try {
      // Check if welcome feature is enabled
      if (config.features?.welcome?.enabled === false) {
        logger.info('Welcome feature is disabled', { guild: member.guild.name });
        return;
      }
      
      const guild = member.guild;
      const welcomeChannel = guild.channels.cache.get(config.settings.welcomeChannelId);
      
      if (!welcomeChannel) {
        logger.warn('Welcome channel not found', { guild: guild.name, channelId: config.settings.welcomeChannelId });
        return;
      }
      
      // Create and send welcome message
      const welcomeMessage = createWelcomeMessage(member, config.settings.hubContent?.welcome);
      await welcomeChannel.send(welcomeMessage);
      
      logger.info('Sent welcome message', { guild: guild.name, user: member.user.tag });
    } catch (error) {
      logger.error('Failed to send welcome message', {
        error: error instanceof Error ? error.message : 'Unknown error',
        user: member?.user?.tag,
        guild: member?.guild?.name
      });
    }
  },
  
  guildMemberRemove: async (member) => {
    try {
      // Check if goodbye feature is enabled
      if (config.features?.goodbye?.enabled === false) {
        logger.info('Goodbye feature is disabled', { guild: member.guild.name });
        return;
      }
      
      const guild = member.guild;
      const goodbyeChannel = guild.channels.cache.get(config.settings.goodbyeChannelId);
      
      if (!goodbyeChannel) {
        logger.warn('Goodbye channel not found', { guild: guild.name, channelId: config.settings.goodbyeChannelId });
        return;
      }
      
      // Create and send goodbye message
      const goodbyeMessage = createGoodbyeMessage(member, config.settings.hubContent?.goodbye);
      await goodbyeChannel.send(goodbyeMessage);
      
      logger.info('Sent goodbye message', { guild: guild.name, user: member.user.tag });
    } catch (error) {
      logger.error('Failed to send goodbye message', {
        error: error instanceof Error ? error.message : 'Unknown error',
        user: member?.user?.tag,
        guild: member?.guild?.name
      });
    }
  }
};

// Export the addon structure
export default {
  name: 'welcome-goodbye',
  version: '1.0.0',
  description: 'Welcome and farewell messages for new and leaving members',
  author: 'OnedEyePete',
  enabled: true,
  events,
  onLoad: async (bot) => {
    const { client, logger: addonLogger } = bot;
    
    addonLogger.info('Welcome-Goodbye addon loading', { 
      clientReady: client.isReady(), 
      clientUser: client.user?.username 
    });
    
    // If client is already ready, setup immediately
    if (client.isReady()) {
      try {
        // Setup logic here if needed when loading after client is ready
        addonLogger.info('Welcome-Goodbye setup completed immediately');
      } catch (error) {
        addonLogger.error('Failed to setup Welcome-Goodbye immediately', { error: error.message });
      }
    }
    
    // Always attach the ready event listener
    client.once('ready', async () => {
      try {
        // Setup logic here
        addonLogger.info('Welcome-Goodbye setup completed on ready event');
      } catch (error) {
        addonLogger.error('Failed to setup Welcome-Goodbye', { error: error.message });
      }
    });
    
    return true;
  },
  onUnload: async () => {
    // Cleanup logic here
    logger.info('Welcome-Goodbye addon unloaded');
    return true;
  }
};
