import { EmbedBuilder } from 'discord.js';

/**
 * Creates a welcome message for new members
 * @param {GuildMember} member - The member who joined
 * @param {Object} config - Welcome message configuration
 * @returns {Object} Discord message object with embed
 */
export function createWelcomeMessage(member, config) {
  const welcomeEmbed = new EmbedBuilder()
    .setTitle(config.title || 'Welcome to the server!')
    .setDescription(config.description || 'A new member has joined the community')
    .setColor(parseInt(config.accentColor || '00FF00', 16))
    .setThumbnail(member.user.displayAvatarURL())
    .addFields(
      { name: 'Member Count', value: `**${member.guild.memberCount}** members now!`, inline: true },
      { name: 'Account Created', value: `<t:${Math.floor(member.user.createdAt.getTime() / 1000)}:R>`, inline: true }
    )
    .setFooter({ text: config.footer || 'Enjoy your stay!' })
    .setTimestamp();

  // Add call to action if available
  if (config.callToAction) {
    welcomeEmbed.addFields({ name: '🚀 Get Started', value: config.callToAction });
  }

  return { embeds: [welcomeEmbed] };
}

/**
 * Creates a goodbye message for leaving members
 * @param {GuildMember} member - The member who left
 * @param {Object} config - Goodbye message configuration
 * @returns {Object} Discord message object with embed
 */
export function createGoodbyeMessage(member, config) {
  const goodbyeEmbed = new EmbedBuilder()
    .setTitle(config.title || 'Member Left')
    .setDescription(config.description || 'A member has left the community')
    .setColor(parseInt(config.accentColor || 'FF0000', 16))
    .setThumbnail(member.user.displayAvatarURL())
    .addFields(
      { name: 'Member Count', value: `**${member.guild.memberCount}** members remaining`, inline: true },
      { name: 'Account Created', value: `<t:${Math.floor(member.user.createdAt.getTime() / 1000)}:R>`, inline: true }
    )
    .setFooter({ text: config.footer || 'We\'ll miss you!' })
    .setTimestamp();

  return { embeds: [goodbyeEmbed] };
}
