import { Client, GatewayIntentBits, Collection, Routes } from "discord.js";
import { REST } from "@discordjs/rest";
import { MongoClient } from "mongodb";
import fs from "fs";
import yaml from "yaml";
import loadAddons from "./addonLoader.js";

const config = yaml.parse(fs.readFileSync("./config.yml", "utf-8"));

const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildVoiceStates,
    GatewayIntentBits.GuildPresences
  ]
});

// Basic logger (replace with a more robust logging solution if needed)
client.logger = {
  info: (...args) => console.log(`[INFO]`, ...args),
  warn: (...args) => console.warn(`[WARN]`, ...args),
  error: (...args) => console.error(`[ERROR]`, ...args),
  debug: (...args) => console.log(`[DEBUG]`, ...args),
};

// This will hold all loaded addons for reference if needed
client.addons = new Collection();

// Slash commands collection
client.commands = new Collection();

// MongoDB Connection
async function connectDB() {
  const uri = config.MongoDBURI;
  if (!uri) {
    client.logger.error("MongoDBURI not found in config.yml. Database features will be unavailable.");
    client.db = null; // Ensure db is null if URI is missing
    return;
  }

  try {
    const mongoClient = new MongoClient(uri);
    await mongoClient.connect();
    const db = mongoClient.db();
    client.logger.info("Connected to MongoDB!");

    // Expose simplified database methods
    client.db = {
      insertOne: async (collectionName, document) => {
        const collection = db.collection(collectionName);
        return await collection.insertOne(document);
      },
      find: async (collectionName, query) => {
        const collection = db.collection(collectionName);
        return await collection.find(query).toArray();
      },
      updateOne: async (collectionName, filter, update) => {
        const collection = db.collection(collectionName);
        return await collection.updateOne(filter, update);
      },
      deleteOne: async (collectionName, filter) => {
        const collection = db.collection(collectionName);
        return await collection.deleteOne(filter);
      },
    };
  } catch (error) {
    client.logger.error("Failed to connect to MongoDB:", error);
    client.db = null; // Ensure db is null on connection failure
  }
}

// Register slash commands
async function registerSlashCommands() {
  const rest = new REST({ version: '10' }).setToken(config.BotToken);

  // Get all commands from the client.commands collection
  const commands = Array.from(client.commands.values()).map(cmd => cmd.data).filter(Boolean);

  try {
    client.logger.info('Started refreshing application (/) commands.');
    client.logger.info(`Found ${commands.length} commands to register.`);

    await rest.put(
      Routes.applicationCommands(config.ClientID),
      { body: commands },
    );

    client.logger.info('Successfully reloaded application (/) commands.');
  } catch (error) {
    client.logger.error('Failed to register slash commands:', error);
  }
}

// Interaction create event handler
function setupInteractionHandler() {
  client.on('interactionCreate', async interaction => {
    if (!interaction.isChatInputCommand()) return;

    const command = client.commands.get(interaction.commandName);

    if (!command) {
      client.logger.warn(`No command matching ${interaction.commandName} was found.`);
      return;
    }

    try {
      await command.execute(interaction);
    } catch (error) {
      client.logger.error('Error executing command:', error);
      const errorMessage = { content: 'There was an error while executing this command!', ephemeral: true };
      
      if (interaction.replied || interaction.deferred) {
        await interaction.followUp(errorMessage);
      } else {
        await interaction.reply(errorMessage);
      }
    }
  });
}

(async () => {
  try {
    await connectDB(); // Connect to DB first
    await client.login(config.BotToken);
    
    // Setup interaction handler before loading addons
    setupInteractionHandler();
    
    await loadAddons(client, true); // enable hot reload

    client.once('ready', async () => {
      client.logger.info(`Logged in as ${client.user.tag}`);
      
      // Register slash commands after bot is ready and addons are loaded
      await registerSlashCommands();
    });
    
  } catch (error) {
    client.logger.error('Failed to start bot:', error);
    process.exit(1);
  }
})();