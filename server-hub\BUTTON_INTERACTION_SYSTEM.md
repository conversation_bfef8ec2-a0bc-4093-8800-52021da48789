# Button Interaction System

The Server Hub addon now includes an advanced button interaction system that prevents spam clicking and manages button states effectively.

## Features

### 🚫 Spam Prevention
- **Cooldown System**: Each button has configurable cooldown periods to prevent rapid clicking
- **User Tracking**: Tracks individual user interactions to enforce per-user cooldowns
- **Automatic Cleanup**: Removes old interaction records to prevent memory leaks

### ⚙️ Configurable Cooldowns
Cooldowns can be configured in `config.yml` under the `buttonSettings` section:

```yaml
buttonSettings:
  enabled: true
  cooldowns:
    server_rules: 5000        # 5 seconds for rules
    get_roles: 3000           # 3 seconds for roles
    create_introduction: 10000 # 10 seconds for introductions
    get_support: 5000         # 5 seconds for support
    create_ticket: 15000      # 15 seconds for tickets
    create_suggestion: 10000  # 10 seconds for suggestions
    default: 2000             # 2 seconds default
  preventSpam: true           # Enable spam prevention
  autoDisable: false          # Auto-disable buttons after use (experimental)
```

### 📊 Button States
- **Enabled**: Normal button state, fully functional
- **Disabled**: <PERSON><PERSON> is disabled and shows reason (e.g., "Cooldown active")
- **Cooldown**: Button temporarily disabled due to recent use

## How It Works

### 1. Button Click Detection
When a user clicks a button, the system:
1. Checks if the button is on cooldown for that user
2. If on cooldown, shows a temporary message and prevents action
3. If allowed, marks the button as used and processes the action

### 2. Cooldown Enforcement
- Each button type has its own cooldown period
- Cooldowns are per-user, not global
- Users can still click other buttons while one is on cooldown

### 3. Spam Prevention
- Prevents rapid clicking of the same button
- Shows user-friendly cooldown messages
- Automatically deletes cooldown messages after 3 seconds

## Button Types and Default Cooldowns

| Button | Cooldown | Description |
|--------|----------|-------------|
| Server Rules | 5 seconds | View server rules |
| Get Roles | 3 seconds | Access role selection |
| Introductions | 10 seconds | Create introduction |
| Support | 5 seconds | Access support system |
| Create Ticket | 15 seconds | Create support ticket |
| Create Suggestion | 10 seconds | Submit suggestion |

## User Experience

### ✅ Normal Usage
- Users can click buttons normally
- No restrictions on first use
- Smooth interaction flow

### ⏳ Cooldown Active
- Clear message showing remaining cooldown time
- Button remains functional but shows cooldown status
- Automatic cleanup of cooldown messages

### 🚫 Spam Prevention
- Prevents abuse of button systems
- Maintains server performance
- Fair usage for all users

## Technical Implementation

### Core Components
- **Button Handler**: Main interaction processing
- **Cooldown Tracker**: User interaction timing
- **Button Utils**: Utility functions for button management
- **Config Integration**: Dynamic cooldown configuration

### Memory Management
- Automatic cleanup of old interaction records
- Configurable cleanup intervals
- Efficient data structures for tracking

### Error Handling
- Graceful fallbacks for failed interactions
- Comprehensive logging for debugging
- User-friendly error messages

## Configuration Options

### Enable/Disable System
```yaml
buttonSettings:
  enabled: true  # Master switch for the system
```

### Custom Cooldowns
```yaml
buttonSettings:
  cooldowns:
    custom_button: 8000  # 8 seconds for custom buttons
```

### Advanced Features
```yaml
buttonSettings:
  preventSpam: true      # Enable spam prevention
  autoDisable: false     # Experimental auto-disable feature
```

## Logging

The system provides comprehensive logging:
- ✅ Button clicks with user information
- ⏳ Cooldown activations
- 🧹 Cleanup operations
- ⚠️ Error conditions

## Future Enhancements

- **Button State Persistence**: Remember button states across bot restarts
- **Advanced Analytics**: Track button usage patterns
- **Dynamic Cooldowns**: Adjust cooldowns based on server activity
- **User Permissions**: Role-based cooldown adjustments

## Troubleshooting

### Common Issues

1. **Buttons not responding**
   - Check if cooldown is active
   - Verify user permissions
   - Check bot logs for errors

2. **Cooldowns too long/short**
   - Adjust values in `config.yml`
   - Restart the addon to apply changes

3. **Memory usage high**
   - Check cleanup intervals
   - Monitor interaction tracker size

### Debug Mode
Enable debug logging to see detailed interaction information:
```yaml
logging:
  logLevel: "debug"
```

## Support

For issues or questions about the button interaction system:
1. Check the logs for error messages
2. Verify configuration settings
3. Test with different user accounts
4. Contact the development team
