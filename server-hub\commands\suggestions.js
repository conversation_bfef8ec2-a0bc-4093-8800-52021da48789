import { <PERSON>lashCommandBuilder, EmbedBuilder, StringSelectMenuBuilder, ActionRowBuilder, MessageFlags, } from 'discord.js';
import { getSuggestionsDb } from '../utils/dbUtils.js';
import fs from 'fs-extra';
import path from 'path';
import yaml from 'js-yaml';
import Logger from '../../../utils/logger.js';

const CONFIG_PATH = './addons/server-hub/config.yml';
const SUGGESTIONS_DB_PATH = './data/suggestions.json';

// Create a logger instance
const logger = new Logger({ level: 'info' });

export const suggestionCommands = [
  new SlashCommandBuilder()
    .setName('suggestions')
    .setDescription('💡 Manage suggestions (Staff only)')
];

export async function handleSuggestionCommand(interaction, bot) {
  let interactionHandled = false;
  
  try {
    // Defer the reply immediately to prevent timeout
    try {
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });
      interactionHandled = true;
    } catch (deferError) {
      logger.error('Failed to defer reply', { error: deferError.message });
      // If defer fails, try to reply immediately
      try {
        await interaction.reply({ 
          content: 'Processing your request...', 
          flags: MessageFlags.Ephemeral 
        });
        interactionHandled = true;
      } catch (replyError) {
        logger.error('Failed to reply', { error: replyError.message });
        return; // Can't handle the interaction, exit
      }
    }
    
    const configPath = path.join(process.cwd(), CONFIG_PATH);

    logger.info('Attempting to load configuration', { path: configPath, cwd: process.cwd() });

    // Check if config file exists
    if (!fs.existsSync(configPath)) {
      logger.error(`❌ Configuration file not found. Path: ${configPath}`);
      await interaction.editReply({
        content: `❌ Configuration file not found. Please contact an administrator. (Path: ${configPath})`,
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const configFile = await fs.readFile(configPath, 'utf8');
    logger.debug('Raw configuration file contents', { configContents: configFile.slice(0, 500) + '...' });

    const config = yaml.load(configFile);
    logger.info('Configuration loaded successfully', { settingsKeys: Object.keys(config.settings || {}), suggestionConfigExists: !!config.settings?.features?.tickets?.suggestions });

    const suggestionConfig = config.settings?.features?.tickets?.suggestions || config.settings?.features?.suggestions;

    if (!suggestionConfig) {
      logger.error('❌ Suggestion configuration not found.');
      await interaction.editReply({
        content: '❌ Suggestion configuration not found. Please contact an administrator.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const staffRoles = suggestionConfig?.staffRoleIds || [];
    const member = interaction.member;

    const validStaffRoles = staffRoles.filter((roleId) => {
      return roleId !== interaction.guildId && roleId.length > 0;
    });

    const hasStaffRole = validStaffRoles.some((roleId) => member.roles.cache.has(roleId));
    const hasAdminPermission = member.permissions.has('Administrator');
    const hasPermission = hasStaffRole || hasAdminPermission;

    logger.info('Suggestion command permission check', {
      userId: interaction.user.id,
      guildId: interaction.guildId,
      staffRoles,
      validStaffRoles,
      memberRoles: Array.from(member.roles.cache.keys()),
      hasStaffRole,
      hasAdminPermission,
      hasPermission,
      suggestionConfig: JSON.stringify(suggestionConfig)
    });

    if (!hasPermission) {
      const requiredRolesText = validStaffRoles.length > 0
        ? validStaffRoles.map((id) => `<@&${id}>`).join(', ')
        : 'No staff roles configured';
      const userRolesText = Array.from(member.roles.cache.keys())
        .filter(id => id !== interaction.guildId) // Filter out @everyone role
        .map((id) => `<@&${id}>`)
        .join(', ') || 'No roles';

      await interaction.editReply({
        content: `❌ You do not have permission to use suggestion management commands.\n\nRequired roles: ${requiredRolesText}\nYour roles: ${userRolesText}\n\nNote: Administrators can also use this command.`,
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const suggestionsDb = await getSuggestionsDb();

    if (!suggestionsDb) {
      logger.error('❌ Database connection failed for suggestions.');
      await interaction.editReply({
        content: '❌ Database connection failed. Please try again later.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const suggestions = await suggestionsDb
      .find({ guildId: interaction.guildId })
      .sort({ createdAt: -1 })
      .limit(25)
      .toArray();

    const activeSuggestions = suggestions.filter(suggestion => suggestion.status !== 'rejected');

    if (activeSuggestions.length === 0) {
      const embed = new EmbedBuilder()
        .setTitle('📋 Suggestions')
        .setDescription('No active suggestions found in this server.')
        .setColor(0xff9900);

      await interaction.editReply({ embeds: [embed], flags: MessageFlags.Ephemeral });
      return;
    }

    // Create select menu for suggestions
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('suggestion_select')
      .setPlaceholder('Select a suggestion to manage')
      .addOptions(activeSuggestions.map(suggestion => ({
        label: `#${suggestion.suggestionNumber} - ${suggestion.title}`.slice(0, 100), // Discord limit
        value: suggestion.suggestionNumber.toString(),
        description: `${suggestion.status} • ${(suggestion.votes?.upvotes || 0) + (suggestion.votes?.downvotes || 0) + (suggestion.votes?.neutral || 0)} votes`.slice(0, 100), // Discord limit
      })));

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);

    const embed = new EmbedBuilder()
      .setTitle('📋 Suggestion Management')
      .setDescription('Select a suggestion from the menu below to view details and manage it.')
      .addFields(
        { name: 'Active Suggestions', value: activeSuggestions.length.toString(), inline: true },
        { name: 'Pending', value: activeSuggestions.filter(s => s.status === 'pending').length.toString(), inline: true },
        { name: 'Approved', value: activeSuggestions.filter(s => s.status === 'approved').length.toString(), inline: true },
        { name: 'Rejected', value: suggestions.filter(s => s.status === 'rejected').length.toString(), inline: true }
      )
      .setColor(0x5865f2)
      .setTimestamp();

    await interaction.editReply({
      embeds: [embed],
      components: [selectRow],
      flags: MessageFlags.Ephemeral
    });
  } catch (error) {
    logger.error(`❌ Error in handleSuggestionCommand: ${error.message}`);
    try {
      if (!interactionHandled) {
        await interaction.reply({ content: '❌ An unexpected error occurred while processing your request.', flags: MessageFlags.Ephemeral });
      } else if (interaction.deferred) {
        await interaction.editReply({ content: '❌ An unexpected error occurred while processing your request.', flags: MessageFlags.Ephemeral });
      } else {
        await interaction.followUp({ content: '❌ An unexpected error occurred while processing your request.', flags: MessageFlags.Ephemeral });
      }
    } catch (replyError) {
      logger.error('Failed to send error reply', { error: replyError.message });
    }
  }
}

export default {
  data: suggestionCommands[0],
  execute: handleSuggestionCommand
};