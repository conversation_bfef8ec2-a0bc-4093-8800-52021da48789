import fs from 'node:fs';
import path from 'node:path';
import YAML from 'js-yaml';

// Read the main project config
const configPath = path.join(process.cwd(), 'config.yml');
let projectConfig;
try {
  const configFile = fs.readFileSync(configPath, 'utf8');
  projectConfig = YAML.load(configFile);
} catch (error) {
  console.error('Failed to load project config:', error);
  projectConfig = {};
}

// Read the addon-specific config (fix the path issue)
const addonConfigPath = path.join(import.meta.dirname, 'config.yml');
let addonConfig;
try {
  const addonConfigFile = fs.readFileSync(addonConfigPath, 'utf8');
  addonConfig = YAML.load(addonConfigFile);
} catch (error) {
  console.error('Failed to load addon config:', error);
  addonConfig = {};
}

// Merge project config with addon config
const config = {
  ...projectConfig,
  addon: addonConfig.addon,
  settings: {
    ...projectConfig.settings,
    ...addonConfig.settings,
    features: {
      ...projectConfig.settings?.features,
      ...addonConfig.settings?.features,
      tickets: {
        ...projectConfig.settings?.features?.tickets,
        ...addonConfig.settings?.features?.tickets,
        workingHours: addonConfig.settings?.features?.tickets?.workingHours
      }
    }
  },
  // Add suggestion categories and other config from addon config
  suggestionCategories: addonConfig.suggestionCategories,
  suggestionsChannelId: addonConfig.settings?.features?.suggestions?.channelId,
  staffRoles: addonConfig.staffRoles,
  roleCategories: addonConfig.roleCategories,
  rules: addonConfig.rules,
  applicationCategories: addonConfig.applicationCategories
};

export { config }; 