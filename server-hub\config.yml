
addon:
  name: "server-hub"
  version: "1.0.0"
  description: "🏠 Central server information hub with suggestions and community features"
  author: "OnedEyePete"
  enabled: true


settings:
  hubChannelId: "1384735024523513906"
  hubContent:
    title: "Welcome to 404 Utopia! 🎮"
    subtitle: "🔞 Adult Gaming Community - 18+ Only 🔞"
    description: "Welcome to our **chill adult gaming hangout!** We're here to make friends, form new relationships, and have a great time together. Whether you're into **SFW fun** or **NSFW content**, we've got something for everyone!"
    
    whatWeOffer:
      title: "🎮 What We Offer"
      content: |
        • **SFW & NSFW Content** - Dedicated spaces for all preferences
        • **Gaming Communities** - Connect with fellow gamers across multiple titles
        • **Friendship & Relationships** - Meet new people and form lasting connections
        • **Chill Atmosphere** - Relaxed environment for adults to unwind
    
    gettingStarted:
      title: "📋 Getting Started"
      content: |
        **Step 1:** Read our server rules (adult-oriented guidelines)
        **Step 2:** Get your roles (colors, games, preferences, etc.)
        **Step 3:** Introduce yourself to the community
        **Step 4:** Start making friends and having fun!
    
    callToAction:
      title: "🌟 Ready to Join the Fun?"
      content: "Click the buttons below to get started in our adult gaming community. Remember, you must be **18+** to participate!"
    
    footer: "**~ Welcome to 404 Utopia - Were a broken heaven! ~**"
    
    accentColor: "9900FF"
  features:
    rules:
      enabled: true
      maintenanceMessage: "🔧 **Rules Under Maintenance**\nThe server rules are currently being updated. Please check back later!"
    roles:
      enabled: true
      maintenanceMessage: "🔧 **Role System Under Maintenance**\nThe role selection feature is currently disabled while we set up new roles. Please check back later!"
    introductions:
      enabled: true
      maintenanceMessage: "🔧 **Introductions Under Maintenance**\nThe introduction system is currently being updated. Please check back later!"
      channelId: "1385699494913380422"
    suggestions:
      enabled: true
      maintenanceMessage: "🔧 **Suggestions Under Maintenance**\nThe suggestion system is currently being updated. Please check back later!"
    applications:
      enabled: false
      maintenanceMessage: "🔧 **Applications Under Maintenance**\nThe application system is currently being updated. Please check back later!"
    tickets:
      enabled: true
      maintenanceMessage: "🔧 **Tickets Under Maintenance**\nThe support ticket system is currently being updated. Please check back later!"
      categoryId: "1386070848326340628"
      staffRoles:
        - "1371665229594890250" # 404 Mastuh
        - "1396653901154881627" # Moderator
        - "1378819238189469807" # Support
        - "1384248737018286106" # Test Moist Cream
      logChannel: "1385826224538128454"

      # ========================================
      # SUGGESTION SYSTEM CONFIGURATION
      # ========================================
      # Controls the suggestion system functionality
      #
      # QUICK SETUP:
      # 1. Set enabled: true to enable suggestions
      # 2. Configure staff roles that can manage suggestions
      # 3. Set suggestion limits and cooldowns
      # 4. Configure notification settings
      #
      suggestions:
        enabled: true
        maintenanceMessage: "🔧 **Suggestions Under Maintenance**\nThe suggestion system is currently being updated. Please check back later!"
        channelId: "1385691916213162115"
        staffRoleIds:
          - "1371665229594890250" # 404 Mastuh
          - "1396653901154881627" # Moderator
          - "1378819238189469807" # Support
          - "1384248737018286106" # Test Moist Cream
        maxSuggestionsPerUser: 10
        globalCooldownMinutes: 5
        enableVoting: true
        logChannelId: "1388929881466998914" # Same as tickets log channel
        notificationSettings:
          notifyOnSubmission: true
          notifyOnStatusChange: true
      
      # ========================================
      # WORKING HOURS CONFIGURATION
      # ========================================
      # Controls when users can create support tickets
      # 
      # QUICK SETUP:
      # 1. Set enabled: true to enforce working hours (false for 24/7)
      # 2. Update timezone to match your location
      # 3. Set daily hours using 24-hour format (9 = 9 AM, 17 = 5 PM)
      # 4. Add emergency categories that bypass working hours
      # 5. Customize the offline message for your users
      #
      workingHours:
        enabled: true  # true = enforce working hours, false = allow tickets 24/7
        
        # Timezone for your support team (use standard timezone names)
        # Examples: "America/New_York", "America/Los_Angeles", "Europe/London", "UTC"
        timezone: "America/New_York"
        
        # Daily schedule (times in 24-hour format: 0-23)
        # enabled: true = support available, false = closed
        # start/end: hours when support begins/ends (e.g., 9 = 9:00 AM, 17 = 5:00 PM)
        #
        # COMMON EXAMPLES:
        # 9 AM - 5 PM:   start: 9,  end: 17
        # 8 AM - 6 PM:   start: 8,  end: 18
        # 10 AM - 4 PM:  start: 10, end: 16
        # 24/7:          enabled: true, start: 0, end: 24
        # Closed:        enabled: false, start: 0, end: 0
        schedule:
          monday:    { enabled: true,  start: 9,  end: 20 }  # 9 AM - 8 PM EST
          tuesday:   { enabled: true,  start: 9,  end: 20 }  # 9 AM - 8 PM EST
          wednesday: { enabled: true,  start: 9,  end: 20 }  # 9 AM - 8 PM EST
          thursday:  { enabled: true,  start: 9,  end: 20 }  # 9 AM - 8 PM EST
          friday:    { enabled: true,  start: 9,  end: 20 }  # 9 AM - 8 PM EST
          saturday:  { enabled: false, start: 0,  end: 0  }  # Closed
          sunday:    { enabled: true, start: 9,  end: 20  }  # Closed
        
        # Categories that bypass working hours (available 24/7)
        # Add category IDs that should always be available for emergencies
        emergencyCategories:
          - "user_report"  # User reports are always available for safety
        
        # Message shown when support is offline
        # Update this to match your team's schedule and contact info
        closedMessage:
          title: "🕐 Support Hours"
          description: "Our support team is currently offline."
          color: 0xFF6B00
          fields:
            - name: "🕒 Business Hours"
              value: "Monday - Friday: 9:00 AM - 8:00 PM EST\nSaturday - Sunday: Closed"
              inline: false
            - name: "🚨 Emergency Support"
              value: "For urgent matters (user reports), support is available 24/7."
              inline: false
            - name: "📅 Next Available"
              value: "Support will resume during our next business hours."
              inline: false
      categories:
        - id: "general_support"
          name: "General Support"
          description: "General questions and support"
          emoji: "🎫"
          useThreads: false
          template: |
            **What can we help you with?**
            Please describe your issue in detail.
            
            **Steps you've already tried:**
            - 
            
            **Additional information:**
            - 
        
        - id: "technical_support"
          name: "Technical Issues"
          description: "Bot or server technical problems"
          emoji: "🔧"
          useThreads: false
          template: |
            **What technical issue are you experiencing?**
            
            **Error message (if any):**
            
            **When did this start happening?**
            
            **What were you trying to do?**
        
        - id: "user_report"
          name: "Report User"
          description: "Report inappropriate behavior"
          emoji: "⚠️"
          useThreads: false
          template: |
            **User being reported:**
            @username or User ID
            
            **What happened?**
            
            **When did this occur?**
            
            **Evidence (screenshots, message links):**
        
        - id: "nsfw_support"
          name: "18+ Support"
          description: "Adult content related support"
          emoji: "🔞"
          useThreads: false
          template: |
            **18+ Support Request**
            
            **Issue description:**
            
            **Age verification status:**
      priorities:
        urgent:
          color: 0xFF0000
          emoji: "🚨"
          autoNotify: true
          escalateAfter: 300000
        high:
          color: 0xFF6B00
          emoji: "⚡"
          autoNotify: true
          escalateAfter: 900000
        medium:
          color: 0x3B82F6
          emoji: "📋"
          autoNotify: false
          escalateAfter: 3600000
        low:
          color: 0x10B981
          emoji: "📝"
          autoNotify: false
          escalateAfter: 7200000

  # Self-assignable roles configuration organized by categories
  roleCategories:
    - id: "interests"
      name: "Interests & Activities"
      description: "Select your interests and activities"
      emoji: "🎯"
      exclusive: false
      roles:
        - id: "1384739376810168330"
          name: "Game Nights"
          emoji: "🎮"
          description: "Join community game nights"
        - id: "1384739476378747050"
          name: "Streaming / Going Live alerts"
          emoji: "📺"
          description: "Get notified when members go live"
        - id: "1384739503301853388"
          name: "New game releases / sales"
          emoji: "🆕"
          description: "Stay updated on new games and sales"
        - id: "1384739546482348053"
          name: "Speed Dating"
          emoji: "💕"
          description: "Participate in speed dating events"
        - id: "1384740052588036270"
          name: "Make Friends"
          emoji: "👥"
          description: "Looking to make new friends"
    
    - id: "relationship_status"
      name: "Relationship Status"
      description: "Let others know your relationship status"
      emoji: "💖"
      exclusive: true
      roles:
        - id: "1399495514273091764"
          name: "Single"
          emoji: "💙"
          description: "Currently single"
        - id: "1399495554651914270"
          name: "In a relationship"
          emoji: "💜"
          description: "Currently in a relationship"
        - id: "1399498354786041956"
          name: "Opened Relationship"
          emoji: "💚"
          description: "In an open relationship"
        - id: "1399498178994241576"
          name: "Married"
          emoji: "💛"
          description: "Married"
        - id: "1399498258782617600"
          name: "Not Looking"
          emoji: "🤍"
          description: "Not looking for relationships"
    
    - id: "dm_preferences"
      name: "DM Preferences"
      description: "Set your DM preferences"
      emoji: "💬"
      exclusive: true
      roles:
        - id: "1399495769651941507"
          name: "Open"
          emoji: "✅"
          description: "Open to receiving direct messages"
        - id: "1399495804757999666"
          name: "Ask"
          emoji: "❓"
          description: "Ask before sending direct messages"
        - id: "1399495832700457221"
          name: "Closed"
          emoji: "❌"
          description: "Not accepting direct messages"
    
    - id: "location"
      name: "Location"
      description: "Share your general location"
      emoji: "🌍"
      exclusive: true
      roles:
        - id: "1399496905809203330"
          name: "North America"
          emoji: "🌎"
          description: "Located in North America"
        - id: "1399496941938671808"
          name: "South America"
          emoji: "🌎"
          description: "Located in South America"
        - id: "1399497079038017546"
          name: "Canada"
          emoji: "🌎"
          description: "Located in Canada"
        - id: "1399497015477534831"
          name: "Asia"
          emoji: "🌍"
          description: "Located in Asia"
        - id: "1399497047345987629"
          name: "Europe"
          emoji: "🌍"
          description: "Located in Europe"
        - id: "1399497134985707540"
          name: "Africa"
          emoji: "🌍"
          description: "Located in Africa"
        - id: "1399497165359419422"
          name: "Australia"
          emoji: "🇦🇺"
          description: "Located in Australia"
        - id: "1399497347330871386"
          name: "Antarctica"
          emoji: "🐧"
          description: "Located in Antarctica"
    
    - id: "age_range"
      name: "Age Range"
      description: "Share your age range"
      emoji: "🎂"
      exclusive: true
      roles:
        - id: "1399497434463469598"
          name: "18-24"
          emoji: "🌟"
          description: "Ages 18-24"
        - id: "1399497577572990987"
          name: "25-34"
          emoji: "⭐"
          description: "Ages 25-34"
        - id: "1399497605691609178"
          name: "35-44"
          emoji: "✨"
          description: "Ages 35-44"
        - id: "1399497636876390451"
          name: "45-54"
          emoji: "💫"
          description: "Ages 45-54"
        - id: "1399497683106009250"
          name: "55+"
          emoji: "🌠"
          description: "Ages 55 and above"

  # 📜 Server Rules - Paginated for better readability
  rules:
    pages:
      - title: "📋 General Rules"
        content: |
          # 📜 Server Rules - Page 1 of 3
          
          ## 1. **Be Respectful** 🤝
          - Treat all members with respect and kindness
          - No harassment, bullying, or personal attacks
          - No hate speech, discrimination, or offensive content
          - Respect others' opinions and engage in constructive discussions
          
          ## 2. **Community Guidelines** 🏠
          - This is an **18+ adult community** - you must be 18 or older
          - Keep conversations appropriate for the channel you're in
          - Use common sense and good judgment in all interactions
          - Help maintain a welcoming environment for everyone
          
          ## 3. **Content Standards** 📝
          - No illegal content or activities
          - No doxxing, sharing personal information, or real-life threats
          - Credit artists when sharing artwork or content
          - No excessive self-promotion without permission
          
      - title: "💬 Communication Rules"
        content: |
          # 📜 Server Rules - Page 2 of 3
          
          ## 4. **Chat Etiquette** 💭
          - No spamming, excessive caps, or emoji spam
          - Keep discussions on-topic for each channel
          - Use English in public channels (other languages in DMs are fine)
          - No mini-modding - let staff handle rule violations
          
          ## 5. **NSFW Content Guidelines** 🔞
          - NSFW content is allowed ONLY in designated channels
          - Always check channel descriptions and rules
          - Respect others' content preferences and boundaries
          - No NSFW content in general/SFW channels
          
          ## 6. **Voice Chat Rules** 🎙️
          - **No Interrupting People**: Don't talk over others or interrupt ongoing conversations
          - **No Commandeering**: Don't take over voice channels or dominate conversations
          - **NSFW Voice Content**: Adult voice content is ONLY allowed in Giggity Club channels
          - **Gaming Courtesy**: Mute your mic while playing games to avoid background noise
          - **No Voice Changers**: No voice changers, soundboards, or ear-rape sounds
          - **Music Policy**: No music in general voice channels unless specifically permitted
          - **Respect Others**: Be mindful of others' conversations and volume levels
          
      - title: "⚖️ Enforcement & Appeals"
        content: |
          # 📜 Server Rules - Page 3 of 3
          
          ## 7. **Gaming & Activities** 🎮
          - Be a good sport in games and activities
          - No cheating, griefing, or toxic behavior in games
          - Respect game-specific channel rules
          - Have fun and include others when possible
          
          ## 8. **Staff Guidelines** 👮
          - Staff decisions are final - respect their authority
          - Use tickets for appeals or serious concerns
          - Don't argue with staff in public channels
          - Report rule violations using appropriate channels
          
          ## 9. **Warning System** ⚠️
          Our warning system is designed to be fair and educational:
          
          **Warning Levels:**
          - **1st Warning**: Educational - Staff explains the rule and why it matters
          - **2nd Warning**: Caution - Reminder that further violations have consequences
          - **3rd Warning**: Final Notice - One more violation results in timeout/restrictions
          
          **What Happens After Warnings:**
          - **4+ Violations**: Timeout (temporary restrictions from certain channels)
          - **Repeated Timeouts**: Kick from server (can rejoin after 24 hours)
          - **Severe Violations**: Immediate kick or ban depending on severity
          
          **Warning Expiration:**
          - Warnings reset after 30 days of good behavior
          - Staff can remove warnings early for exceptional improvement
          
          ## 10. **Appeals & Second Chances** 🤝
          - If you believe a warning was given unfairly, use the ticket system
          - Staff will review the situation and may adjust or remove warnings
          - We believe in second chances and want to help everyone succeed
          
          ---
          
          💡 **Remember**: These rules help maintain a positive adult community for everyone. When in doubt, ask staff for clarification!
          
suggestionCategories:
  - id: "server"
    name: "Server Improvement"
    emoji: "⚡"
    description: "Ideas to improve the server experience"
    color: 0x3B82F6
  - id: "features"
    name: "New Features"
    emoji: "✨"
    description: "Suggestions for new bot features or channels"
    color: 0x10B981
  - id: "events"
    name: "Events & Activities"
    emoji: "🎉"
    description: "Ideas for community events and activities"
    color: 0xF59E0B
  - id: "gaming"
    name: "Gaming Content"
    emoji: "🎮"
    description: "Gaming-related suggestions and improvements"
    color: 0x8B5CF6


applicationCategories:
  - id: "moderator"
    name: "Moderator"
    emoji: "🛡️"
    description: "Help moderate the server and enforce rules"
    color: 0xFF6B35
  - id: "event_host"
    name: "Event Host"
    emoji: "🎉"
    description: "Organize and host community events"
    color: 0x10B981
  - id: "content_creator"
    name: "Content Creator"
    emoji: "🎨"
    description: "Create content for the server"
    color: 0x8B5CF6
  - id: "support_team"
    name: "Support Team"
    emoji: "🎫"
    description: "Help users with support tickets"
    color: 0x3B82F6


database:
  collections:
    - "tickets"
    - "suggestions"
    - "applications"
    - "hub_stats"


logging:
  enabled: true
  logLevel: "info"

# Button interaction settings
buttonSettings:
  enabled: true
  cooldowns:
    server_rules: 5000        # 5 seconds for rules
    get_roles: 3000           # 3 seconds for roles
    create_introduction: 10000 # 10 seconds for introductions
    get_support: 5000         # 5 seconds for support
    create_ticket: 15000      # 15 seconds for tickets
    create_suggestion: 10000  # 10 seconds for suggestions
    default: 2000             # 2 seconds default
  preventSpam: true           # Enable spam prevention
  autoDisable: false          # Auto-disable buttons after use (experimental) 