import {
  handleConfirmCloseTicket,
  handleCancelCloseTicket,
  handleClaimTicket,
  handleSetPriority,
  handleTransferTicket,
  handleCloseTicket,
  handleTicketButtonInteractions,
  handleTicketSelectMenuInteractions,
} from './tickets.js';
import {
  handleSuggestionVote,
  handleSuggestionStatusChange,
  handleSuggestionSelect,
  handleSuggestionsBack,
} from './suggestions.js';
import fs from 'fs';
import path from 'path';
import YAML from 'js-yaml';
import Logger from '../../../utils/logger.js';
import { MessageFlags } from 'discord.js';
import { logButtonInteraction } from '../utils/buttonUtils.js';

// Create a logger instance
const logger = new Logger({ level: 'info' });

// Track button interactions to prevent spam clicking
const buttonInteractionTracker = new Map();

// Default button cooldown settings (in milliseconds) - can be overridden by config
let BUTTON_COOLDOWNS = {
  server_rules: 5000,        // 5 seconds for rules
  get_roles: 3000,           // 3 seconds for roles
  create_introduction: 10000, // 10 seconds for introductions
  get_support: 5000,         // 5 seconds for support
  create_ticket: 15000,      // 15 seconds for tickets
  create_suggestion: 10000,  // 10 seconds for suggestions
  default: 2000              // 2 seconds default
};

/**
 * Update button cooldowns from config
 * @param {Object} config - Addon configuration
 */
export function updateButtonCooldowns(config) {
  if (config.settings?.buttonSettings?.enabled && config.settings?.buttonSettings?.cooldowns) {
    BUTTON_COOLDOWNS = {
      ...BUTTON_COOLDOWNS,
      ...config.settings.buttonSettings.cooldowns
    };
    logger.info('Updated button cooldowns from config', { cooldowns: BUTTON_COOLDOWNS });
  }
}

/**
 * Check if a button interaction is on cooldown
 * @param {string} buttonId - The button custom ID
 * @param {string} userId - The user ID
 * @returns {boolean} - True if on cooldown, false if allowed
 */
function isButtonOnCooldown(buttonId, userId) {
  const key = `${buttonId}_${userId}`;
  const now = Date.now();
  
  if (buttonInteractionTracker.has(key)) {
    const lastInteraction = buttonInteractionTracker.get(key);
    const cooldown = BUTTON_COOLDOWNS[buttonId] || BUTTON_COOLDOWNS.default;
    
    if (now - lastInteraction < cooldown) {
      return true;
    }
  }
  
  return false;
}

/**
 * Mark a button interaction as used
 * @param {string} buttonId - The button custom ID
 * @param {string} userId - The user ID
 */
function markButtonInteraction(buttonId, userId) {
  const key = `${buttonId}_${userId}`;
  buttonInteractionTracker.set(key, Date.now());
  
  // Clean up old entries after 1 hour
  setTimeout(() => {
    buttonInteractionTracker.delete(key);
  }, 3600000);
}

/**
 * Create a disabled version of a button to prevent spam clicking
 * @param {Object} originalButton - The original button configuration
 * @param {string} reason - Reason for disabling
 * @returns {Object} - Disabled button configuration
 */
function createDisabledButton(originalButton, reason = 'Cooldown active') {
  return {
    ...originalButton,
    disabled: true,
    label: `${originalButton.label} (${reason})`,
    style: 2 // Secondary style for disabled state
  };
}

/**
 * Handle main hub button interactions with cooldown and spam prevention
 * @param {Object} interaction - Discord interaction object
 * @param {Object} config - Addon configuration
 * @returns {boolean} - True if handled, false if not
 */
export const handleMainHubButtons = async (interaction, config) => {
  const buttonId = interaction.customId;
  
  // Check if this is a main hub button
  if (!['server_rules', 'get_roles', 'create_introduction', 'get_support'].includes(buttonId)) {
    return false;
  }
  
  // Check cooldown
  if (isButtonOnCooldown(buttonId, interaction.user.id)) {
    const cooldown = BUTTON_COOLDOWNS[buttonId] || BUTTON_COOLDOWNS.default;
    const remaining = Math.ceil(cooldown / 1000);
    
    await interaction.reply({
      content: `⏳ **Button Cooldown Active**\nPlease wait ${remaining} seconds before clicking this button again.`,
      flags: MessageFlags.Ephemeral
    });
    
    // Auto-delete after 3 seconds
    setTimeout(async () => {
      try {
        await interaction.deleteReply();
      } catch (error) {
        // Ignore deletion errors
      }
    }, 3000);
    
    return true;
  }
  
  // Mark button as used
  markButtonInteraction(buttonId, interaction.user.id);
  
  // Log button click using the utility function
  logButtonInteraction(interaction, buttonId, 'clicked');
  
  return false; // Let the main handler process the button
};

export const handleTicketButtons = async (interaction, config) => {
  // Use the main ticket button handler from tickets.js
  await handleTicketButtonInteractions(interaction, config);
};

export const handleSuggestionButtons = async (interaction) => {
  // Load config from the local config file for suggestions
  const configPath = path.join(process.cwd(), 'addons', 'server-hub', 'config.yml');
  const configFile = fs.readFileSync(configPath, 'utf8');
  const config = YAML.load(configFile);

  // Debug logging
  logger.debug('Suggestion button handler called with customId:', { customId: interaction.customId });

  // Suggestion vote buttons
  if (interaction.customId.startsWith('suggestion_vote_up_')) {
    const suggestionNumber = parseInt(interaction.customId.replace('suggestion_vote_up_', ''));
    await handleSuggestionVote(interaction, suggestionNumber, 'up');
    return;
  }

  if (interaction.customId.startsWith('suggestion_vote_down_')) {
    const suggestionNumber = parseInt(interaction.customId.replace('suggestion_vote_down_', ''));
    await handleSuggestionVote(interaction, suggestionNumber, 'down');
    return;
  }

  if (interaction.customId.startsWith('suggestion_vote_neutral_')) {
    const suggestionNumber = parseInt(interaction.customId.replace('suggestion_vote_neutral_', ''));
    await handleSuggestionVote(interaction, suggestionNumber, 'neutral');
    return;
  }

  // Suggestion status change buttons (staff only)
  if (interaction.customId.startsWith('suggestion_approve_')) {
    const suggestionNumber = parseInt(interaction.customId.replace('suggestion_approve_', ''));
    await handleSuggestionStatusChange(interaction, suggestionNumber, 'approved');
    return;
  }

  if (interaction.customId.startsWith('suggestion_reject_')) {
    const suggestionNumber = parseInt(interaction.customId.replace('suggestion_reject_', ''));
    await handleSuggestionStatusChange(interaction, suggestionNumber, 'rejected');
    return;
  }

  if (interaction.customId.startsWith('suggestion_consider_')) {
    const suggestionNumber = parseInt(interaction.customId.replace('suggestion_consider_', ''));
    await handleSuggestionStatusChange(interaction, suggestionNumber, 'considering');
    return;
  }

  if (interaction.customId.startsWith('suggestion_implement_')) {
    const suggestionNumber = parseInt(interaction.customId.replace('suggestion_implement_', ''));
    await handleSuggestionStatusChange(interaction, suggestionNumber, 'implemented');
    return;
  }

  // Suggestion manage button (for future use)
  if (interaction.customId.startsWith('suggestion_manage_')) {
    const suggestionNumber = parseInt(interaction.customId.replace('suggestion_manage_', ''));
    // TODO: Implement suggestion management interface
    await interaction.reply({
      content: '❌ Suggestion management interface is not yet implemented.',
      flags: MessageFlags.Ephemeral, // Ephemeral
    });
    return;
  }

  // Back button for suggestions
  if (interaction.customId === 'suggestions_back') {
    logger.debug('Handling suggestions_back button');
    await handleSuggestionsBack(interaction);
    return;
  }

  // If we get here, no handler was found
  logger.debug('No suggestion button handler found for:', { customId: interaction.customId });
};

export const handleSuggestionSelectMenus = async (interaction) => {
  // Handle suggestion select menu
  if (interaction.customId === 'suggestion_select') {
    await handleSuggestionSelect(interaction);
    return;
  }
};

export const handleTicketSelectMenus = async (interaction, config) => {
  // Use the main ticket select menu handler from tickets.js
  await handleTicketSelectMenuInteractions(interaction, config);
};

// Clean up old button interaction tracking entries periodically
setInterval(() => {
  const now = Date.now();
  const maxAge = 3600000; // 1 hour
  
  for (const [key, timestamp] of buttonInteractionTracker.entries()) {
    if (now - timestamp > maxAge) {
      buttonInteractionTracker.delete(key);
    }
  }
}, 300000); // Clean up every 5 minutes