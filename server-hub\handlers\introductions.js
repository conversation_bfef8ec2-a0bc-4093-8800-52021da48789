import { 
    ActionRowBuilder, 
    EmbedBuilder, 
    ButtonBuilder, 
    ButtonStyle, 
    ChannelType, 
    PermissionsBitField,
    ModalBuilder,
    TextInputBuilder,
    TextInputStyle,
    StringSelectMenuBuilder,
    MessageFlags
} from 'discord.js';
import fs from 'fs-extra';
import path from 'path';
import Logger from '../../../utils/logger.js';

// Create a logger instance
const logger = new Logger({ level: 'info' });

// Simple JSON file-based database helpers for introductions
const DB_PATH = path.join(process.cwd(), 'data', 'introductions.json');

// Ensure data directory exists
await fs.ensureDir(path.dirname(DB_PATH));

// Database helper functions
async function getData(client, table, query) {
    try {
        await fs.ensureFile(DB_PATH);
        const data = await fs.readJson(DB_PATH).catch(() => ({}));
        const tableData = data[table] || [];
        
        if (!query || Object.keys(query).length === 0) {
            return tableData;
        }
        
        return tableData.filter(item => {
            return Object.keys(query).every(key => item[key] === query[key]);
        });
    } catch (error) {
        logger.error('Error reading introduction data:', error);
        return [];
    }
}

async function saveData(client, table, newData) {
    try {
        await fs.ensureFile(DB_PATH);
        const data = await fs.readJson(DB_PATH).catch(() => ({}));
        if (!data[table]) data[table] = [];
        
        // Remove existing item with same ID if it exists
        data[table] = data[table].filter(item => item.id !== newData.id);
        data[table].push(newData);
        
        await fs.writeJson(DB_PATH, data, { spaces: 2 });
        return newData;
    } catch (error) {
        logger.error('Error saving introduction data:', error);
        throw error;
    }
}

class IntroductionBuilder {
    constructor(client) {
        this.client = client;
        this.activeBuilders = new Map();
        this.ephemeralCleanup = new Map();
    }

    async handleCreateIntroductionButton(interaction, config) {
        try {
            // Check if introductions are enabled
            if (config.settings.features.introductions?.enabled === false) {
                const maintenanceMsg = config.settings.features.introductions?.maintenanceMessage || 
                    "🔧 **Introductions Under Maintenance**\nThe introduction system is currently being updated. Please check back later!";
                
                await interaction.reply({
                    content: maintenanceMsg,
                    ephemeral: true
                });
                return;
            }

            await interaction.deferReply({ ephemeral: true });
            await this.startBuilder(interaction, config);
        } catch (error) {
            logger.error('Error handling create introduction button:', error);
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ An error occurred while starting the introduction builder.',
                    ephemeral: true
                }).catch(() => {});
            }
        }
    }

    async startBuilder(interaction, config) {
        const userId = interaction.user.id;
        const introductionsChannelId = config.settings.features.introductions?.channelId;
        
        if (!introductionsChannelId) {
            return interaction.editReply({ 
                content: "❌ Introductions channel is not configured. Please contact an administrator." 
            });
        }

        const targetChannel = interaction.guild.channels.cache.get(introductionsChannelId);
        if (!targetChannel) {
            return interaction.editReply({ 
                content: "❌ Introductions channel not found. Please contact an administrator." 
            });
        }

        // Check bot permissions
        const botPermissions = targetChannel.permissionsFor(interaction.guild.members.me);
        if (!botPermissions.has(PermissionsBitField.Flags.SendMessages) || !botPermissions.has(PermissionsBitField.Flags.EmbedLinks)) {
            return interaction.editReply({
                content: "❌ I don't have permission to send messages and embeds in the introductions channel."
            });
        }

        // Check if user already has an active builder
        if (this.activeBuilders.has(userId)) {
            const existing = this.activeBuilders.get(userId);
            try {
                await existing.interaction.editReply({ 
                    content: 'This introduction builder has been replaced by a new one.', 
                    components: [], 
                    embeds: [] 
                });
            } catch {}
        }

        const builderId = `introduction_${userId}_${Date.now()}`;
        
        // Initial embed with introduction template
        const initialEmbed = new EmbedBuilder()
            .setColor(0x9900FF)  // Using the accent color from config
            .setTitle('👋 My Introduction')
            .setDescription('Click the buttons below to customize your introduction!')
            .setAuthor({ 
                name: interaction.user.displayName, 
                iconURL: interaction.user.displayAvatarURL() 
            })
            .setThumbnail(interaction.user.displayAvatarURL())
            .setTimestamp()
            .setFooter({ text: '~ Welcome to 404 Utopia! ~' });

        const data = {
            builderId,
            targetChannel,
            embed: initialEmbed,
            user: interaction.user,
            creationTimestamp: Date.now()
        };

        const builderEmbed = new EmbedBuilder()
            .setColor(0x9900FF)
            .setTitle('👋 Introduction Builder')
            .setDescription(`Building your introduction for ${targetChannel}.\nThe preview below will update as you make changes.`)
            .setAuthor({ 
                name: interaction.user.displayName, 
                iconURL: interaction.user.displayAvatarURL() 
            });

        const components = this.createBuilderComponents(builderId);

        await interaction.editReply({
            embeds: [builderEmbed, data.embed],
            components,
        });
        const message = await interaction.fetchReply();

        const builderInstance = { data, interaction, message };
        this.activeBuilders.set(userId, builderInstance);
        
        // Timeout cleanup
        setTimeout(() => {
            const currentInstance = this.activeBuilders.get(userId);
            if (currentInstance && currentInstance.data.builderId === builderId) {
                logger.info(`Timing out introduction builder for user ${userId}`);
                interaction.editReply({ 
                    content: 'Introduction builder has timed out.', 
                    embeds: [], 
                    components: [] 
                }).catch(() => {});
                this.activeBuilders.delete(userId);
                this.cleanupAllEphemeralsForBuilder(builderId);
            }
        }, 900000); // 15 minutes
    }

    createBuilderComponents(builderId) {
        const components = [];

        // Button Row 1: Core Content
        components.push(new ActionRowBuilder().addComponents(
            new ButtonBuilder().setCustomId(`${builderId}_title`).setLabel('Title').setStyle(ButtonStyle.Primary).setEmoji('✏️'),
            new ButtonBuilder().setCustomId(`${builderId}_description`).setLabel('About Me').setStyle(ButtonStyle.Primary).setEmoji('📝'),
            new ButtonBuilder().setCustomId(`${builderId}_color`).setLabel('Color').setStyle(ButtonStyle.Secondary).setEmoji('🎨')
        ));
        
        // Button Row 2: Personal Info Fields
        components.push(new ActionRowBuilder().addComponents(
            new ButtonBuilder().setCustomId(`${builderId}_addfield`).setLabel('Add Field').setStyle(ButtonStyle.Success).setEmoji('➕'),
            new ButtonBuilder().setCustomId(`${builderId}_removefield`).setLabel('Remove Field').setStyle(ButtonStyle.Danger).setEmoji('➖'),
            new ButtonBuilder().setCustomId(`${builderId}_clearfields`).setLabel('Clear Fields').setStyle(ButtonStyle.Danger).setEmoji('🧹')
        ));

        // Button Row 3: Actions
        components.push(new ActionRowBuilder().addComponents(
            new ButtonBuilder().setCustomId(`${builderId}_send`).setLabel('Post Introduction').setStyle(ButtonStyle.Success).setEmoji('📤'),
            new ButtonBuilder().setCustomId(`${builderId}_cancel`).setLabel('Cancel').setStyle(ButtonStyle.Danger).setEmoji('❌')
        ));
        
        return components;
    }

    async messageComponentRun(interaction) {
        const customId = interaction.customId;
        const userId = interaction.user.id;
        
        // Handle modal submissions
        if (interaction.isModalSubmit()) {
            return this.handleModalSubmit(interaction);
        }
        
        const parts = customId.split('_');
        if (parts.length < 3) return false; 
        
        let builderId;
        if (parts[0] === 'introduction' && parts.length >= 3) {
            builderId = `${parts[0]}_${parts[1]}_${parts[2]}`;
        } else {
            return false;
        }

        const builderInstance = this.activeBuilders.get(userId);

        if (builderInstance && builderInstance.data.builderId === builderId) {
            if (Date.now() - builderInstance.data.creationTimestamp > 900000) {
                interaction.reply({ 
                    content: 'This introduction builder has expired.', 
                    flags: MessageFlags.Ephemeral 
                });
                this.activeBuilders.delete(userId);
                return true;
            }

            try {
                const action = customId.substring(builderId.length + 1);
                if (action.startsWith('confirm')) {
                    await this.handleConfirmation(interaction, builderInstance, action);
                } else {
                    await this.handleBuilderInteraction(interaction, builderInstance);
                }
                return true;
            } catch (error) {
                logger.error(`Error in introduction builder interaction:`, error);
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({ 
                        content: 'An error occurred.', 
                        flags: MessageFlags.Ephemeral 
                    }).catch(() => {});
                }
                return true;
            }
        }
        
        return false;
    }

    async handleModalSubmit(interaction) {
        const customId = interaction.customId;
        const userId = interaction.user.id;
        
        const parts = customId.split('_');
        if (parts.length < 4 || parts[0] !== 'introduction') return false;
        
        const builderId = `${parts[0]}_${parts[1]}_${parts[2]}`;
        const builderInstance = this.activeBuilders.get(userId);
        
        if (!builderInstance || builderInstance.data.builderId !== builderId) {
            await interaction.reply({
                content: '❌ Introduction builder session not found or expired.',
                ephemeral: true
            });
            return true;
        }

        try {
            const modalType = parts[4]; // modal type is the 5th part
            const { data } = builderInstance;

            switch (modalType) {
                case 'title':
                    const title = interaction.fields.getTextInputValue('text').trim();
                    if (title) {
                        data.embed.setTitle(title);
                    } else {
                        data.embed.setTitle(null);
                    }
                    break;

                case 'description':
                    const description = interaction.fields.getTextInputValue('description').trim();
                    if (description) {
                        data.embed.setDescription(description);
                    } else {
                        data.embed.setDescription('Click the buttons below to customize your introduction!');
                    }
                    break;

                case 'color':
                    const colorInput = interaction.fields.getTextInputValue('text').trim();
                    if (colorInput) {
                        try {
                            let color = colorInput.replace('#', '');
                            if (color.length === 3) {
                                color = color.split('').map(c => c + c).join('');
                            }
                            const colorInt = parseInt(color, 16);
                            if (!isNaN(colorInt) && colorInt >= 0 && colorInt <= 0xFFFFFF) {
                                data.embed.setColor(colorInt);
                            }
                        } catch (error) {
                            logger.warn('Invalid color provided:', colorInput);
                        }
                    }
                    break;



                case 'addfield':
                    const fieldName = interaction.fields.getTextInputValue('name').trim();
                    const fieldValue = interaction.fields.getTextInputValue('value').trim();
                    const inlineInput = interaction.fields.getTextInputValue('inline').trim().toLowerCase();
                    const inline = inlineInput === 'true' || inlineInput === 'yes' || inlineInput === '1';

                    if (fieldName && fieldValue) {
                        const currentFields = data.embed.data.fields || [];
                        if (currentFields.length < 25) {
                            data.embed.addFields({ name: fieldName, value: fieldValue, inline });
                        }
                    }
                    break;


            }

            await interaction.deferUpdate();
            await this.updateBuilderInterface(builderInstance);
            return true;

        } catch (error) {
            logger.error('Error handling introduction modal submit:', error);
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ An error occurred while processing your input.',
                    ephemeral: true
                }).catch(() => {});
            }
            return true;
        }
    }

    async handleBuilderInteraction(interaction, builderInstance) {
        const { data } = builderInstance;
        const action = interaction.customId.substring(data.builderId.length + 1);



        switch(action) {
            case 'title':
            case 'description':
            case 'color':
            case 'addfield':
                return this.showModal(interaction, builderInstance, action);

            case 'removefield_select':
                return this.handleRemoveFieldSelect(interaction, builderInstance);

            case 'clearfields':
                await interaction.deferUpdate();
                data.embed.setFields([]);
                await this.updateBuilderInterface(builderInstance);
                const clearReply = await interaction.followUp({ 
                    content: '✅ All fields have been cleared.', 
                    flags: MessageFlags.Ephemeral 
                });
                setTimeout(async () => {
                    try {
                        await interaction.deleteReply(clearReply.id);
                    } catch {}
                }, 5000);
                return;
            
            case 'removefield':
                return this.handleRemoveField(interaction, builderInstance);
            case 'send':
                return this.handleSend(interaction, builderInstance);
            case 'cancel':
                return this.handleCancel(interaction, builderInstance);
            
            default:
                logger.warn('Unhandled introduction builder action', {
                    action,
                    customId: interaction.customId,
                    builderId: data.builderId
                });
                await interaction.reply({
                    content: `❌ Unknown action: ${action}`,
                    ephemeral: true
                }).catch(() => {});
                return;
        }
    }

    async showModal(interaction, builderInstance, type) {
        const { data } = builderInstance;
        const { builderId } = data;
        const modal = new ModalBuilder().setCustomId(`${builderId}_modal_${type}`);

        switch (type) {
            case 'title':
                modal.setTitle('Set Introduction Title').addComponents(new ActionRowBuilder().addComponents(
                    new TextInputBuilder()
                        .setCustomId('text')
                        .setLabel('Title')
                        .setStyle(TextInputStyle.Short)
                        .setValue(data.embed.data.title || '')
                        .setRequired(false)
                        .setMaxLength(256)
                        .setPlaceholder('e.g., "👋 My Introduction", "Hello everyone!"')
                ));
                break;
            case 'description':
                modal.setTitle('Tell Us About Yourself').addComponents(
                    new ActionRowBuilder().addComponents(
                        new TextInputBuilder()
                            .setCustomId('description')
                            .setLabel('About Me')
                            .setStyle(TextInputStyle.Paragraph)
                            .setValue(data.embed.data.description || '')
                            .setRequired(false)
                            .setMaxLength(4000)
                            .setPlaceholder('Tell us about yourself, your interests, hobbies, what games you play, etc.')
                    )
                );
                break;
            case 'color':
                modal.setTitle('Set Introduction Color').addComponents(new ActionRowBuilder().addComponents(
                    new TextInputBuilder()
                        .setCustomId('text')
                        .setLabel('Hex Color Code (e.g. #9900FF)')
                        .setStyle(TextInputStyle.Short)
                        .setValue(typeof data.embed.data.color === 'number' ? `#${data.embed.data.color.toString(16)}` : data.embed.data.color || '')
                        .setRequired(false)
                        .setPlaceholder('#9900FF')
                ));
                break;

            case 'addfield':
                modal.setTitle('Add Information Field').addComponents(
                    new ActionRowBuilder().addComponents(
                        new TextInputBuilder()
                            .setCustomId('name')
                            .setLabel('Field Name')
                            .setStyle(TextInputStyle.Short)
                            .setRequired(true)
                            .setPlaceholder('e.g., "Age", "Location", "Favorite Games"')
                    ),
                    new ActionRowBuilder().addComponents(
                        new TextInputBuilder()
                            .setCustomId('value')
                            .setLabel('Field Value')
                            .setStyle(TextInputStyle.Paragraph)
                            .setRequired(true)
                            .setPlaceholder('Your answer here...')
                    ),
                    new ActionRowBuilder().addComponents(
                        new TextInputBuilder()
                            .setCustomId('inline')
                            .setLabel('Inline (true/false)')
                            .setStyle(TextInputStyle.Short)
                            .setValue('true')
                            .setPlaceholder('true')
                    )
                );
                break;

        }

        await interaction.showModal(modal);
    }



    // Additional helper methods would go here...
    async updateBuilderInterface(builderInstance) {
        try {
            const { data } = builderInstance;
            const components = this.createBuilderComponents(data.builderId);

            const builderEmbed = new EmbedBuilder()
                .setColor(0x9900FF)
                .setTitle('👋 Introduction Builder')
                .setDescription(`Building your introduction for ${data.targetChannel}.\nThe preview below will update as you make changes.`)
                .setAuthor({ 
                    name: data.user.displayName, 
                    iconURL: data.user.displayAvatarURL() 
                });

            await builderInstance.interaction.editReply({
                embeds: [builderEmbed, data.embed],
                components
            });
        } catch (error) {
            logger.error('Error updating builder interface:', error);
        }
    }

    async handleRemoveFieldSelect(interaction, builderInstance) {
        try {
            const { data } = builderInstance;
            const fieldIndex = parseInt(interaction.values[0]);
            const fields = data.embed.data.fields || [];

            if (fieldIndex >= 0 && fieldIndex < fields.length) {
                const removedField = fields[fieldIndex];
                fields.splice(fieldIndex, 1);
                data.embed.setFields(fields);

                await interaction.update({
                    content: `✅ Removed field: "${removedField.name}"`,
                    embeds: [],
                    components: []
                });

                await this.updateBuilderInterface(builderInstance);
            } else {
                await interaction.update({
                    content: '❌ Invalid field selection.',
                    embeds: [],
                    components: []
                });
            }
        } catch (error) {
            logger.error('Error removing field:', error);
            await interaction.update({
                content: '❌ An error occurred while removing the field.',
                embeds: [],
                components: []
            }).catch(() => {});
        }
    }

    async handleSend(interaction, builderInstance) {
        try {
            await interaction.deferUpdate();
            const { data } = builderInstance;

            // Send the introduction to the target channel
            const introMessage = await data.targetChannel.send({
                embeds: [data.embed]
            });

            // Update the builder interface to show success
            await interaction.editReply({
                content: `✅ Your introduction has been posted in ${data.targetChannel}!\n[View your introduction](${introMessage.url})`,
                embeds: [],
                components: []
            });

            // Clean up the builder
            this.activeBuilders.delete(interaction.user.id);
            this.cleanupAllEphemeralsForBuilder(data.builderId);

            logger.info(`Introduction posted by ${data.user.tag} in ${data.targetChannel.name}`);
        } catch (error) {
            logger.error('Error sending introduction:', error);
            await interaction.followUp({
                content: '❌ Failed to post your introduction. Please try again.',
                ephemeral: true
            }).catch(() => {});
        }
    }

    async handleCancel(interaction, builderInstance) {
        try {
            await interaction.update({
                content: '❌ Introduction builder cancelled.',
                embeds: [],
                components: []
            });

            this.activeBuilders.delete(interaction.user.id);
            this.cleanupAllEphemeralsForBuilder(builderInstance.data.builderId);
        } catch (error) {
            logger.error('Error cancelling introduction builder:', error);
        }
    }

    cleanupAllEphemeralsForBuilder(builderId) {
        // Clean up any ephemeral messages for this builder
        const ephemeralMessages = this.ephemeralCleanup.get(builderId) || [];
        ephemeralMessages.forEach(async (messageData) => {
            try {
                await messageData.interaction.deleteReply(messageData.messageId);
            } catch {}
        });
        this.ephemeralCleanup.delete(builderId);
    }

    // Helper method for removing fields
    async handleRemoveField(interaction, builderInstance) {
        const { data } = builderInstance;
        const fields = data.embed.data.fields || [];
        
        if (fields.length === 0) {
            await interaction.reply({
                content: '❌ No fields to remove.',
                ephemeral: true
            });
            return;
        }

        // Create options for each field
        const options = fields.map((field, index) => ({
            label: field.name.substring(0, 100),
            value: index.toString(),
            description: field.value.substring(0, 100)
        }));

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`${data.builderId}_removefield_select`)
            .setPlaceholder('Select field to remove...')
            .addOptions(options);

        await interaction.reply({
            content: 'Select the field you want to remove:',
            components: [new ActionRowBuilder().addComponents(selectMenu)],
            ephemeral: true
        });
    }
}

// Create a global instance
const introductionBuilder = new IntroductionBuilder();

export async function handleCreateIntroductionButton(interaction, config) {
    return introductionBuilder.handleCreateIntroductionButton(interaction, config);
}

export async function handleIntroductionInteraction(interaction) {
    return introductionBuilder.messageComponentRun(interaction);
} 