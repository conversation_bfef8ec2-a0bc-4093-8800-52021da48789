import { 
  EmbedBuilder, 
  MessageFlags
} from 'discord.js';
import Logger from '../../../utils/logger.js';
import { scheduleEphemeralDeletion } from '../utils/ephemeralUtils.js';

// Create a logger instance
const logger = new Logger({ level: 'info' });

export async function handleGetRolesButton(interaction, config) {
  const features = config.settings.features || {};

  if (features.roles?.enabled === false) {
    // Maintenance message using standard embeds
    const maintenanceEmbed = new EmbedBuilder()
      .setColor(0xF59E0B)
      .setTitle('🔧 Role System Under Maintenance')
      .setDescription(features.roles.maintenanceMessage || 'The role selection feature is currently disabled while we set up new roles. Please check back later!')
      .setTimestamp();

    await interaction.reply({
      embeds: [maintenanceEmbed],
      flags: MessageFlags.Ephemeral
    });

    // Auto-delete after 10 seconds
    scheduleEphemeralDeletion(interaction.id, interaction, 10000);
    return;
  }

  const roleCategories = config.settings.roleCategories;

  if (!roleCategories || roleCategories.length === 0) {
    const errorEmbed = new EmbedBuilder()
      .setColor(0xEF4444)
      .setTitle('❌ No Role Categories Available')
      .setDescription('No role categories are currently configured. Please contact an administrator.')
      .setTimestamp();

    await interaction.reply({
      embeds: [errorEmbed],
      flags: MessageFlags.Ephemeral
    });

    // Auto-delete error messages after 15 seconds
    scheduleEphemeralDeletion(interaction.id, interaction, 15000);
    return;
  }

  try {
    // Validate for duplicate role IDs across all categories
    const allRoleIds = new Set();
    const duplicateIds = new Set();
    
    for (const category of roleCategories) {
      if (!category.roles) continue;
      
      for (const role of category.roles) {
        if (allRoleIds.has(role.id)) {
          duplicateIds.add(role.id);
          logger.warn(`Duplicate role ID found: ${role.id} in category ${category.name}`, {
            roleId: role.id,
            roleName: role.name,
            categoryId: category.id,
            categoryName: category.name
          });
        } else {
          allRoleIds.add(role.id);
        }
      }
    }

    if (duplicateIds.size > 0) {
      logger.error('Configuration error: Duplicate role IDs detected', {
        duplicateIds: Array.from(duplicateIds),
        totalCategories: roleCategories.length
      });
      
      const errorEmbed = new EmbedBuilder()
        .setColor(0xEF4444)
        .setTitle('❌ Configuration Error')
        .setDescription('There are duplicate role IDs in the configuration. Please contact an administrator to fix this issue.')
        .setTimestamp();

      await interaction.reply({
        embeds: [errorEmbed],
        flags: MessageFlags.Ephemeral
      });

      scheduleEphemeralDeletion(interaction.id, interaction, 15000);
      return;
    }

    // Create main embed with enhanced styling
    const mainEmbed = new EmbedBuilder()
      .setColor(0x5865F2)
      .setTitle('🎭 **Select Your Roles**')
      .setDescription('Choose from the categories below to customize your server experience. Each category has different selection rules.')
      .addFields({
        name: '📋 **How to Use**',
        value: '• **Exclusive categories**: You can only select one role\n• **Multi-select categories**: You can select multiple roles\n• **Current roles are pre-selected** - deselect to remove them\n• Use the dropdown menus below to make your selections',
        inline: false
      })
      .setFooter({ text: 'Role selections are saved automatically' })
      .setTimestamp();

    // Get user's current roles for pre-selection and status display
    const member = interaction.member;
    const userRoles = member.roles.cache;

    // Create select menus for each category (max 5 per message)
    const components = [];
    const maxCategoriesPerMessage = 5;
    const categoriesToShow = roleCategories.slice(0, maxCategoriesPerMessage);

    for (const category of categoriesToShow) {
      if (!category.roles || category.roles.length === 0) continue;

      // Validate for duplicate role IDs within this category
      const categoryRoleIds = new Set();
      const validRoles = [];
      
      for (const role of category.roles) {
        if (categoryRoleIds.has(role.id)) {
          logger.warn(`Skipping duplicate role ID ${role.id} in category ${category.name}`);
          continue;
        }
        categoryRoleIds.add(role.id);
        validRoles.push(role);
      }

      if (validRoles.length === 0) continue;

      // Find which roles the user currently has in this category
      const userRolesInCategory = validRoles.filter(role => userRoles.has(role.id));
      const defaultValues = userRolesInCategory.map(role => role.id);

      // Create options for the select menu
      const options = [];
      for (const role of validRoles) {
        const hasRole = userRoles.has(role.id);
        options.push({
          label: role.name,
          description: hasRole 
            ? `✅ Currently selected - ${role.description || `You have the ${role.name} role`}`
            : role.description || `Select the ${role.name} role`,
          value: role.id,
          emoji: { name: role.emoji || '🎯' },
          default: hasRole // Pre-select if user has this role
        });
      }

      // Create select menu component using raw structure for new component system
      const selectMenuComponent = {
        type: 3, // String select menu
        custom_id: `role_select_${category.id}`,
        placeholder: userRolesInCategory.length > 0 
          ? `Current: ${userRolesInCategory.map(r => r.name).join(', ')}` 
          : `Select ${category.name} ${category.emoji}`,
        min_values: 0,
        max_values: category.exclusive ? 1 : validRoles.length,
        options: options
      };

      // Add the select menu to an action row
      const actionRow = {
        type: 1, // Action row
        components: [selectMenuComponent]
      };
      components.push(actionRow);
    }

    // Add category information to embed with current role status
    let categoryInfo = '';
    for (const category of categoriesToShow) {
      const selectionType = category.exclusive ? '**[One Choice]**' : '**[Multi-Select]**';
      
      // Find user's current roles in this category
      const userRolesInCategory = category.roles?.filter(role => userRoles.has(role.id)) || [];
      const currentRolesText = userRolesInCategory.length > 0 
        ? `\n*Current: ${userRolesInCategory.map(r => `${r.emoji || '🎯'} ${r.name}`).join(', ')}*`
        : '';
      
      categoryInfo += `${category.emoji} **${category.name}** ${selectionType}\n${category.description}${currentRolesText}\n\n`;
    }

    mainEmbed.addFields({
      name: '🎯 **Available Categories**',
      value: categoryInfo.trim(),
      inline: false
    });

    // If there are more than 5 categories, add a note
    if (roleCategories.length > maxCategoriesPerMessage) {
      mainEmbed.addFields({
        name: '📝 **Note**',
        value: `Showing ${maxCategoriesPerMessage} of ${roleCategories.length} categories. More categories available in additional messages.`,
        inline: false
      });
    }

    await interaction.reply({
      embeds: [mainEmbed],
      components: components,
      flags: MessageFlags.Ephemeral
    });

    // Log who clicked the Get Roles button with a check mark
    logger.info(`✅ ${interaction.user.username} → Roles Interface`);

    // Auto-delete after 60 seconds
    scheduleEphemeralDeletion(interaction.id, interaction, 60000);

  } catch (error) {
    logger.error('Error in role selection', error);

    const errorEmbed = new EmbedBuilder()
      .setColor(0xEF4444)
      .setTitle('❌ Error Loading Roles')
      .setDescription('There was an error loading the role selection interface. Please try again or contact an administrator.')
      .setTimestamp();

    await interaction.reply({
      embeds: [errorEmbed],
      flags: MessageFlags.Ephemeral
    });

    // Auto-delete error messages after 15 seconds
    scheduleEphemeralDeletion(interaction.id, interaction, 15000);
  }
}

export async function handleRoleSelect(interaction, config) {
  const customId = interaction.customId;
  const categoryId = customId.replace('role_select_', '');
  const selectedRoleIds = interaction.values;

  try {
    const roleCategories = config.settings.roleCategories;
    const category = roleCategories.find(cat => cat.id === categoryId);

    if (!category) {
      throw new Error(`Category ${categoryId} not found`);
    }

    const guild = interaction.guild;
    const member = interaction.member;

    // Get current member roles
    const currentRoles = member.roles.cache;
    const rolesToAdd = [];
    const rolesToRemove = [];

    // If this is an exclusive category, remove all other roles from this category first
    if (category.exclusive) {
      for (const roleConfig of category.roles) {
        const roleId = roleConfig.id;
        if (currentRoles.has(roleId) && !selectedRoleIds.includes(roleId)) {
          rolesToRemove.push(roleId);
        }
      }
    }

    // Process selected roles
    for (const roleId of selectedRoleIds) {
      if (!currentRoles.has(roleId)) {
        rolesToAdd.push(roleId);
      }
    }

    // Prevent adding duplicate roles across categories
    const duplicateRoles = [];
    for (const roleToAdd of rolesToAdd) {
      // Check if this role exists in any other category
      for (const otherCategory of roleCategories) {
        if (otherCategory.id !== categoryId) {
          const roleInOtherCategory = otherCategory.roles?.find(r => r.id === roleToAdd);
          if (roleInOtherCategory) {
            duplicateRoles.push({
              roleId: roleToAdd,
              roleName: roleInOtherCategory.name,
              categoryName: otherCategory.name
            });
          }
        }
      }
    }

    // If duplicate roles found, prevent adding and show an error
    if (duplicateRoles.length > 0) {
      const errorEmbed = new EmbedBuilder()
        .setColor(0xEF4444)
        .setTitle('❌ Role Selection Error')
        .setDescription('You cannot add roles that already exist in other categories.')
        .addFields(
          duplicateRoles.map(duplicate => ({
            name: `🚫 Duplicate Role: ${duplicate.roleName}`,
            value: `This role is already part of the **${duplicate.categoryName}** category.`,
            inline: false
          }))
        )
        .setTimestamp();

      await interaction.reply({
        embeds: [errorEmbed],
        flags: MessageFlags.Ephemeral
      });

      // Auto-delete error messages after 15 seconds
      scheduleEphemeralDeletion(interaction.id, interaction, 15000);
      return;
    }

    // Process deselected roles (for multi-select categories)
    if (!category.exclusive) {
      for (const roleConfig of category.roles) {
        const roleId = roleConfig.id;
        if (currentRoles.has(roleId) && !selectedRoleIds.includes(roleId)) {
          rolesToRemove.push(roleId);
        }
      }
    }

    // Apply role changes
    if (rolesToAdd.length > 0) {
      await member.roles.add(rolesToAdd);
    }
    if (rolesToRemove.length > 0) {
      await member.roles.remove(rolesToRemove);
    }

    // Create success response with detailed change information
    const selectedRoleNames = selectedRoleIds.map(roleId => {
      const roleConfig = category.roles.find(r => r.id === roleId);
      return roleConfig ? `${roleConfig.emoji} ${roleConfig.name}` : roleId;
    });

    const addedRoleNames = rolesToAdd.map(roleId => {
      const roleConfig = category.roles.find(r => r.id === roleId);
      return roleConfig ? `${roleConfig.emoji} ${roleConfig.name}` : roleId;
    });

    const removedRoleNames = rolesToRemove.map(roleId => {
      const roleConfig = category.roles.find(r => r.id === roleId);
      return roleConfig ? `${roleConfig.emoji} ${roleConfig.name}` : roleId;
    });

    const successEmbed = new EmbedBuilder()
      .setColor(0x10B981)
      .setTitle('✅ Roles Updated Successfully')
      .setDescription(`Your roles in the **${category.name}** category have been updated!`);

    // Add field for current roles
    if (selectedRoleNames.length > 0) {
      successEmbed.addFields({
        name: '🎯 **Current Roles**',
        value: selectedRoleNames.join('\n'),
        inline: false
      });
    }

    // Add field for added roles if any
    if (addedRoleNames.length > 0) {
      successEmbed.addFields({
        name: '➕ **Added**',
        value: addedRoleNames.join('\n'),
        inline: true
      });
    }

    // Add field for removed roles if any
    if (removedRoleNames.length > 0) {
      successEmbed.addFields({
        name: '➖ **Removed**',
        value: removedRoleNames.join('\n'),
        inline: true
      });
    }

    // If no roles selected, show that
    if (selectedRoleNames.length === 0) {
      successEmbed.addFields({
        name: '🎯 **Current Roles**',
        value: 'None selected',
        inline: false
      });
    }

    successEmbed.setTimestamp();

    await interaction.reply({
      embeds: [successEmbed],
      flags: MessageFlags.Ephemeral
    });

    // Log who selected roles with a check mark
    const roleList = selectedRoleNames.length > 0 ? selectedRoleNames.join(', ') : 'None';
    logger.info(`✅ ${interaction.user.username} → ${category.name}: ${roleList}`);

    // Auto-delete success message after 10 seconds
    scheduleEphemeralDeletion(interaction.id, interaction, 10000);

  } catch (error) {
    logger.error('Error in role selection', error);

    const errorEmbed = new EmbedBuilder()
      .setColor(0xEF4444)
      .setTitle('❌ Error Updating Roles')
      .setDescription('There was an error updating your roles. Please try again or contact an administrator.')
      .addFields({
        name: '🔍 **Error Details**',
        value: error.message || 'Unknown error occurred',
        inline: false
      })
      .setTimestamp();

    await interaction.reply({
      embeds: [errorEmbed],
      flags: MessageFlags.Ephemeral
    });

    // Auto-delete error messages after 15 seconds
    scheduleEphemeralDeletion(interaction.id, interaction, 15000);
  }
}