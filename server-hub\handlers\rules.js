import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  MessageFlags
} from 'discord.js';
import Logger from '../../../utils/logger.js';
import { scheduleEphemeralDeletion } from '../utils/ephemeralUtils.js';

// Create a logger instance
const logger = new Logger({ level: 'info' });

// Store active rule sessions to track pagination
const activeRuleSessions = new Map();

export async function handleRulesButton(interaction, config) {
  const features = config.settings.features || {};

  if (features.rules?.enabled === false) {
    const maintenanceEmbed = new EmbedBuilder()
      .setColor(0xF59E0B)
      .setTitle('🔧 Rules Under Maintenance')
      .setDescription(features.rules.maintenanceMessage || '🔧 **Feature Under Maintenance**\nThis feature is currently disabled.')
      .setTimestamp();

    await interaction.reply({
      embeds: [maintenanceEmbed],
      flags: MessageFlags.Ephemeral
    });

    // Auto-delete after 10 seconds
    scheduleEphemeralDeletion(interaction.id, interaction, 10000);
    return;
  }

  // Get rules pages from config
  const rulesPages = config.settings.rules?.pages || [];
  
  if (rulesPages.length === 0) {
    const errorEmbed = new EmbedBuilder()
      .setColor(0xEF4444)
      .setTitle('❌ No Rules Available')
      .setDescription('Server rules are not currently configured. Please contact an administrator.')
      .setTimestamp();

    await interaction.reply({
      embeds: [errorEmbed],
      flags: MessageFlags.Ephemeral
    });

    // Auto-delete after 15 seconds
    scheduleEphemeralDeletion(interaction.id, interaction, 15000);
    return;
  }

  // Start with page 1
  const currentPage = 0;
  const sessionId = `rules_${interaction.user.id}_${Date.now()}`;
  
  // Store session data
  activeRuleSessions.set(sessionId, {
    userId: interaction.user.id,
    currentPage: currentPage,
    totalPages: rulesPages.length,
    pages: rulesPages,
    createdAt: Date.now()
  });

  try {
    const { embed, components } = buildRulesPage(sessionId, currentPage, rulesPages);

    await interaction.reply({
      embeds: [embed],
      components: components,
      flags: MessageFlags.Ephemeral
    });

    // Log who clicked the Server Rules button with a check mark
    logger.info(`✅ ${interaction.user.username} → Rules Page 1/${rulesPages.length}`);

    // Auto-delete after 5 minutes
    scheduleEphemeralDeletion(interaction.id, interaction, 300000);

    // Clean up session after 10 minutes
    setTimeout(() => {
      activeRuleSessions.delete(sessionId);
    }, 600000);

    // If this is an ephemeral message, we can't disable the original button
    // But we can show a message indicating the button was used
    // The cooldown system in buttonHandler.js will prevent spam clicking

  } catch (error) {
    logger.error('Error displaying rules', error);

    const errorEmbed = new EmbedBuilder()
      .setColor(0xEF4444)
      .setTitle('❌ Error Loading Rules')
      .setDescription('There was an error loading the server rules. Please try again or contact an administrator.')
      .setTimestamp();

    await interaction.reply({
      embeds: [errorEmbed],
      flags: MessageFlags.Ephemeral
    });

    // Auto-delete error messages after 15 seconds
    scheduleEphemeralDeletion(interaction.id, interaction, 15000);
  }
}

export async function handleRulesNavigation(interaction, config) {
  const customId = interaction.customId;
  
  // Parse the custom ID: rules_action_userId_timestamp
  const parts = customId.split('_');
  const action = parts[1]; // first, prev, next, last
  const sessionId = `rules_${parts[2]}_${parts[3]}`; // Reconstruct: rules_userId_timestamp

  // Get session data
  const session = activeRuleSessions.get(sessionId);
  
  if (!session) {
    const errorEmbed = new EmbedBuilder()
      .setColor(0xEF4444)
      .setTitle('❌ Session Expired')
      .setDescription('This rules session has expired. Please click the "Server Rules" button again to view the rules.')
      .setTimestamp();

    await interaction.reply({
      embeds: [errorEmbed],
      flags: MessageFlags.Ephemeral
    });

    // Auto-delete after 10 seconds
    scheduleEphemeralDeletion(interaction.id, interaction, 10000);
    return;
  }

  // Verify user ownership
  if (session.userId !== interaction.user.id) {
    const errorEmbed = new EmbedBuilder()
      .setColor(0xEF4444)
      .setTitle('❌ Access Denied')
      .setDescription('You can only navigate your own rules session. Please click the "Server Rules" button to start your own session.')
      .setTimestamp();

    await interaction.reply({
      embeds: [errorEmbed],
      flags: MessageFlags.Ephemeral
    });

    // Auto-delete after 10 seconds
    scheduleEphemeralDeletion(interaction.id, interaction, 10000);
    return;
  }

  let newPage = session.currentPage;

  // Handle navigation
  switch (action) {
    case 'first':
      newPage = 0;
      break;
    case 'prev':
      newPage = Math.max(0, session.currentPage - 1);
      break;
    case 'next':
      newPage = Math.min(session.totalPages - 1, session.currentPage + 1);
      break;
    case 'last':
      newPage = session.totalPages - 1;
      break;
    default:
      logger.warn('Unknown navigation action', { action, sessionId });
      return;
  }

  // Update session
  session.currentPage = newPage;
  activeRuleSessions.set(sessionId, session);

  try {
    const { embed, components } = buildRulesPage(sessionId, newPage, session.pages);

    await interaction.update({
      embeds: [embed],
      components: components
    });

    // Log who clicked what button with a check mark
    logger.info(`✅ ${interaction.user.username} → Rules Page ${newPage + 1}/${session.totalPages}`);

  } catch (error) {
    logger.error('Error navigating rules', error);

    const errorEmbed = new EmbedBuilder()
      .setColor(0xEF4444)
      .setTitle('❌ Navigation Error')
      .setDescription('There was an error navigating the rules. Please try again.')
      .setTimestamp();

    await interaction.reply({
      embeds: [errorEmbed],
      flags: MessageFlags.Ephemeral
    });

    // Auto-delete error messages after 15 seconds
    scheduleEphemeralDeletion(interaction.id, interaction, 15000);
  }
}

function buildRulesPage(sessionId, pageIndex, pages) {
  const page = pages[pageIndex];
  const totalPages = pages.length;
  const pageNumber = pageIndex + 1;

  // Create embed for current page
  const embed = new EmbedBuilder()
    .setColor(0x57F287)
    .setTitle(`📗 ${page.title}`)
    .setDescription(page.content)
    .setFooter({ 
      text: `Page ${pageNumber} of ${totalPages} • Use the buttons below to navigate`,
      iconURL: null
    })
    .setTimestamp();

  // Create navigation buttons
  const navigationRow = new ActionRowBuilder();

  // First page button
  navigationRow.addComponents(
    new ButtonBuilder()
      .setCustomId(`rules_first_${sessionId.replace('rules_', '')}`)
      .setLabel('⏮️')
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(pageIndex === 0)
  );

  // Previous page button
  navigationRow.addComponents(
    new ButtonBuilder()
      .setCustomId(`rules_prev_${sessionId.replace('rules_', '')}`)
      .setLabel('◀️ Previous')
      .setStyle(ButtonStyle.Primary)
      .setDisabled(pageIndex === 0)
  );

  // Page indicator (disabled button showing current page)
  navigationRow.addComponents(
    new ButtonBuilder()
      .setCustomId(`rules_page_indicator`)
      .setLabel(`${pageNumber}/${totalPages}`)
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(true)
  );

  // Next page button
  navigationRow.addComponents(
    new ButtonBuilder()
      .setCustomId(`rules_next_${sessionId.replace('rules_', '')}`)
      .setLabel('Next ▶️')
      .setStyle(ButtonStyle.Primary)
      .setDisabled(pageIndex === totalPages - 1)
  );

  // Last page button
  navigationRow.addComponents(
    new ButtonBuilder()
      .setCustomId(`rules_last_${sessionId.replace('rules_', '')}`)
      .setLabel('⏭️')
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(pageIndex === totalPages - 1)
  );

  return {
    embed: embed,
    components: [navigationRow]
  };
}

// Clean up old sessions periodically (run every 10 minutes)
setInterval(() => {
  const now = Date.now();
  const maxAge = 600000; // 10 minutes

  for (const [sessionId, session] of activeRuleSessions.entries()) {
    if (now - session.createdAt > maxAge) {
      activeRuleSessions.delete(sessionId);
      logger.info('Cleaned up expired rules session', { sessionId });
    }
  }
}, 600000);