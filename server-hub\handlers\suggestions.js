import {
  ButtonInteraction,
  StringSelectMenuInteraction,
  ModalSubmitInteraction,
  MessageFlags,
  StringSelectMenuBuilder,
  ActionRowBuilder,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
  EmbedBuilder,
  ChannelType,
  ButtonBuilder,
  ButtonStyle,
  User,
  GuildMember,
  PermissionsBitField,
  AttachmentBuilder,
  Message,
  ComponentType,
  InteractionResponse,
  Colors,
} from 'discord.js';
import Logger from '../../../utils/logger.js';
import { getSuggestionsDb } from '../utils/dbUtils.js';
import { 
  getSuggestionFromCache, 
  queueSuggestionUpdate, 
  invalidateSuggestionCache 
} from '../utils/suggestionCache.js';
import { scheduleEphemeralDeletion, updateEphemeralStatus } from '../utils/ephemeralUtils.js';
import fs from 'fs';
import path from 'path';
import YAML from 'js-yaml';

// Create a logger instance
const logger = new Logger({ level: 'info' });

// Enhanced cache system with TTL
const userCooldowns = new Map();
const userSuggestionCounts = new Map();
const configCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
const COOLDOWN_CLEANUP_INTERVAL = 10 * 60 * 1000; // 10 minutes

// Constants for better maintainability
const CONSTANTS = {
  MAX_SUGGESTION_TITLE_LENGTH: 100,
  MIN_SUGGESTION_TITLE_LENGTH: 5,
  MAX_SUGGESTION_DESCRIPTION_LENGTH: 1500,
  MIN_SUGGESTION_DESCRIPTION_LENGTH: 20,
  MAX_SUGGESTION_REASON_LENGTH: 750,
  MAX_SUGGESTION_TAGS_LENGTH: 200,
  MAX_REJECTION_REASON_LENGTH: 500,
  MIN_REJECTION_REASON_LENGTH: 10,
  DEFAULT_MAX_SUGGESTIONS_PER_USER: 10,
  DEFAULT_COOLDOWN_MINUTES: 5,
  SUGGESTION_NUMBER_MIN: 10000,
  SUGGESTION_NUMBER_MAX: 99999,
  MAX_TAGS_PER_SUGGESTION: 5,
  THREAD_AUTO_ARCHIVE_DURATION: 10080, // 7 days in minutes
};

// Status configuration
const STATUS_CONFIG = {
  pending: { 
    color: Colors.Blue, 
    emoji: '⏳', 
    label: 'Pending',
    description: 'Awaiting review by staff' 
  },
  approved: { 
    color: Colors.Green, 
    emoji: '✅', 
    label: 'Approved',
    description: 'Approved and being considered for implementation' 
  },
  rejected: { 
    color: Colors.Red, 
    emoji: '❌', 
    label: 'Rejected',
    description: 'Rejected and no longer active' 
  },
  considering: { 
    color: Colors.Orange, 
    emoji: '🤔', 
    label: 'Under Review',
    description: 'Being carefully reviewed by staff' 
  },
  implemented: { 
    color: Colors.Green, 
    emoji: '🚀', 
    label: 'Implemented',
    description: 'Successfully implemented' 
  },
};

// Enhanced utility functions
class SuggestionsUtility {
  static async getUserSuggestionCount(userId, guildId) {
    const cacheKey = `${guildId}-${userId}`;
    const cached = userSuggestionCounts.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return cached.count;
    }

    try {
      const suggestionsDb = await getSuggestionsDb();
      const count = await suggestionsDb.countDocuments({ 
        userId, 
        guildId,
        status: { $ne: 'rejected' }
      });
      
      userSuggestionCounts.set(cacheKey, { count, timestamp: Date.now() });
      return count;
    } catch (error) {
      logger.error('Failed to get user suggestion count', {
        error: error.message,
        userId,
        guildId
      });
      return 0;
    }
  }

  static async checkUserCooldown(userId, categoryId, cooldownMinutes) {
    try {
      const userCooldownList = userCooldowns.get(userId) || [];
      const categoryCooldown = userCooldownList.find(c => c.categoryId === categoryId);
      
      if (!categoryCooldown) return false;
      
      const cooldownEnd = new Date(categoryCooldown.lastSubmission.getTime() + (cooldownMinutes * 60 * 1000));
      const isOnCooldown = new Date() < cooldownEnd;
      
      // Clean up expired cooldowns
      if (!isOnCooldown) {
        const updatedList = userCooldownList.filter(c => c.categoryId !== categoryId);
        if (updatedList.length > 0) {
          userCooldowns.set(userId, updatedList);
        } else {
          userCooldowns.delete(userId);
        }
      }
      
      return isOnCooldown;
    } catch (error) {
      logger.error('Error checking user cooldown', { error: error.message, userId, categoryId });
      return false;
    }
  }

  static updateUserCooldown(userId, categoryId) {
    try {
      const userCooldownList = userCooldowns.get(userId) || [];
      const existingIndex = userCooldownList.findIndex(c => c.categoryId === categoryId);
      
      const cooldown = {
        userId,
        categoryId,
        lastSubmission: new Date(),
      };
      
      if (existingIndex >= 0) {
        userCooldownList[existingIndex] = cooldown;
      } else {
        userCooldownList.push(cooldown);
      }
      
      userCooldowns.set(userId, userCooldownList);
    } catch (error) {
      logger.error('Error updating user cooldown', { error: error.message, userId, categoryId });
    }
  }

  static async hasRequiredRoles(member, requiredRoles) {
    if (!requiredRoles || requiredRoles.length === 0) return true;
    if (!member || !member.roles) return false;
    
    try {
      return requiredRoles.some(roleId => member.roles.cache.has(roleId));
    } catch (error) {
      logger.error('Error checking required roles', { error: error.message, memberId: member?.id });
      return false;
    }
  }

  static generateSuggestionNumber() {
    return Math.floor(Math.random() * (CONSTANTS.SUGGESTION_NUMBER_MAX - CONSTANTS.SUGGESTION_NUMBER_MIN)) + CONSTANTS.SUGGESTION_NUMBER_MIN;
  }

  static capitalizeFirst(str) {
    if (!str || typeof str !== 'string') return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  static truncateText(text, maxLength) {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  }

  static sanitizeInput(input, maxLength) {
    if (!input || typeof input !== 'string') return '';
    return input.trim().substring(0, maxLength);
  }

  static parseTags(tagsInput) {
    if (!tagsInput) return [];
    
    return tagsInput
      .split(',')
      .map(tag => tag.trim().toLowerCase())
      .filter(tag => tag.length > 0 && tag.length <= 20)
      .slice(0, CONSTANTS.MAX_TAGS_PER_SUGGESTION);
  }

  static async loadConfig() {
    const cacheKey = 'config';
    const cached = configCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return cached.config;
    }

    try {
      const configPath = path.join(process.cwd(), 'addons', 'server-hub', 'config.yml');
      const configFile = fs.readFileSync(configPath, 'utf8');
      const config = YAML.load(configFile);
      
      // Debug logging
      logger.info('Loaded config structure', {
        hasSettings: !!config.settings,
        hasFeatures: !!config.settings?.features,
        hasSuggestions: !!config.settings?.features?.suggestions,
        suggestionsChannelId: config.settings?.features?.suggestions?.channelId,
        configKeys: Object.keys(config),
        settingsKeys: Object.keys(config.settings || {}),
        featuresKeys: Object.keys(config.settings?.features || {}),
        fullConfig: JSON.stringify(config, null, 2)
      });
      
      configCache.set(cacheKey, { config, timestamp: Date.now() });
      return config;
    } catch (error) {
      logger.error('Failed to load config', { error: error.message });
      return {};
    }
  }

  static validateChannelType(channel) {
    const supportedTypes = [
      ChannelType.GuildText,
      ChannelType.GuildAnnouncement,
      ChannelType.GuildForum,
    ];
    
    return supportedTypes.includes(channel?.type) || 
           (channel?.isTextBased && channel?.permissionsFor?.(channel.client.user)?.has('SendMessages'));
  }

  static async checkBotPermissions(channel, member) {
    const requiredPermissions = [
      PermissionsBitField.Flags.SendMessages,
      PermissionsBitField.Flags.EmbedLinks,
      PermissionsBitField.Flags.AddReactions,
      PermissionsBitField.Flags.UseExternalEmojis,
      PermissionsBitField.Flags.ReadMessageHistory,
    ];

    const botPermissions = channel.permissionsFor(member);
    return requiredPermissions.filter(perm => !botPermissions?.has(perm));
  }
}

// Enhanced embed creation
class SuggestionEmbedBuilder {
  static createSuggestionEmbed(suggestion, category, isAnonymous = false) {
    try {
      const status = suggestion.status || 'pending';
      const statusInfo = STATUS_CONFIG[status] || STATUS_CONFIG.pending;
      
      const embed = new EmbedBuilder()
        .setTitle(`${statusInfo.emoji} Suggestion #${suggestion.suggestionNumber}`)
        .setDescription(`**${suggestion.title}**\n\n${suggestion.description}`)
        .setColor(statusInfo.color)
        .setTimestamp();

      // Add status-specific description
      if (status === 'rejected') {
        embed.setDescription(
          `**${suggestion.title}**\n\n${suggestion.description}\n\n🔒 **This suggestion has been ${statusInfo.description.toLowerCase()}.**`
        );
      } else if (status === 'implemented') {
        embed.setDescription(
          `**${suggestion.title}**\n\n${suggestion.description}\n\n🎉 **This suggestion has been ${statusInfo.description.toLowerCase()}!**`
        );
      } else if (status === 'approved') {
        embed.setDescription(
          `**${suggestion.title}**\n\n${suggestion.description}\n\n✅ **This suggestion has been ${statusInfo.description.toLowerCase()}.**`
        );
      }

      // Author field
      embed.addFields({
        name: '👤 Suggested by',
        value: isAnonymous ? 'Anonymous' : `<@${suggestion.userId}>`,
        inline: true
      });

      // Category and creation date
      embed.addFields(
        { 
          name: '📂 Category', 
          value: category?.name || 'Unknown', 
          inline: true 
        },
        { 
          name: '📅 Created', 
          value: `<t:${Math.floor((suggestion.createdAt || new Date()).getTime() / 1000)}:F>`, 
          inline: true 
        }
      );

      // Status and votes
      embed.addFields(
        { 
          name: '📊 Status', 
          value: statusInfo.label, 
          inline: true 
        },
        { 
          name: '🗳️ Votes', 
          value: `👍 ${suggestion.votes?.upvotes || 0} • 👎 ${suggestion.votes?.downvotes || 0} • 🤷 ${suggestion.votes?.neutral || 0}`, 
          inline: true 
        }
      );

      // Additional reasoning
      if (suggestion.reason && suggestion.reason.trim() !== 'No additional reasoning provided.') {
        embed.addFields({ 
          name: '💭 Reasoning', 
          value: SuggestionsUtility.truncateText(suggestion.reason, 1024), 
          inline: false 
        });
      }

      // Rejection details
      if (status === 'rejected') {
        if (suggestion.rejectionReason) {
          embed.addFields({ 
            name: '❌ Rejection Reason', 
            value: SuggestionsUtility.truncateText(suggestion.rejectionReason, 1024), 
            inline: false 
          });
        }
        
        if (suggestion.rejectedBy) {
          embed.addFields({ 
            name: '👤 Rejected by', 
            value: `<@${suggestion.rejectedBy}>`, 
            inline: true 
          });
        }

        if (suggestion.rejectedAt) {
          embed.addFields({ 
            name: '📅 Rejected on', 
            value: `<t:${Math.floor(suggestion.rejectedAt.getTime() / 1000)}:F>`, 
            inline: true 
          });
        }
      }

      // Implementation details
      if (status === 'implemented' && suggestion.implementationDate) {
        embed.addFields({ 
          name: '🚀 Implemented on', 
          value: `<t:${Math.floor(suggestion.implementationDate.getTime() / 1000)}:F>`, 
          inline: true 
        });
      }

      // Tags
      if (suggestion.tags && suggestion.tags.length > 0) {
        embed.addFields({ 
          name: '🏷️ Tags', 
          value: suggestion.tags.map(tag => `\`${tag}\``).join(' '), 
          inline: false 
        });
      }

      // Footer
      embed.setFooter({ 
        text: `Suggestion #${suggestion.suggestionNumber} • ${category?.name || 'Unknown Category'}` 
      });

      return embed;
    } catch (error) {
      logger.error('Error creating suggestion embed', { 
        error: error.message, 
        suggestionId: suggestion?.suggestionNumber 
      });
      
      // Return a basic error embed
      return new EmbedBuilder()
        .setTitle('❌ Error Loading Suggestion')
        .setDescription('There was an error loading this suggestion.')
        .setColor(Colors.Red);
    }
  }

  static createActionButtons(suggestionNumber, suggestionStatus) {
    try {
      // Don't show voting buttons for finalized suggestions
      if (suggestionStatus === 'rejected' || suggestionStatus === 'implemented') {
        return [];
      }

      return [
        new ActionRowBuilder()
          .addComponents(
            new ButtonBuilder()
              .setCustomId(`suggestion_vote_up_${suggestionNumber}`)
              .setLabel('Upvote')
              .setEmoji('👍')
              .setStyle(ButtonStyle.Success),
            new ButtonBuilder()
              .setCustomId(`suggestion_vote_down_${suggestionNumber}`)
              .setLabel('Downvote')
              .setEmoji('👎')
              .setStyle(ButtonStyle.Danger),
            new ButtonBuilder()
              .setCustomId(`suggestion_vote_neutral_${suggestionNumber}`)
              .setLabel('Neutral')
              .setEmoji('🤷')
              .setStyle(ButtonStyle.Secondary)
          )
      ];
    } catch (error) {
      logger.error('Error creating action buttons', { error: error.message, suggestionNumber });
      return [];
    }
  }

  static createStatusEmbed(title, description, type = 'info') {
    const colors = {
      success: Colors.Green,
      error: Colors.Red,
      warning: Colors.Orange,
      info: Colors.Blue
    };

    const emojis = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };

    return new EmbedBuilder()
      .setTitle(`${emojis[type]} ${title}`)
      .setDescription(description)
      .setColor(colors[type] || Colors.Blue)
      .setTimestamp();
  }
}

// Main handler functions
export async function handleCreateSuggestionButton(interaction, config) {
  try {
    const features = config.settings?.features || {};
    const suggestionConfig = config.settings?.features?.suggestions || {};

    // Enhanced maintenance check
    if (features.suggestions?.enabled === false) {
      const maintenanceEmbed = SuggestionEmbedBuilder.createStatusEmbed(
        'Suggestions Under Maintenance',
        features.suggestions.maintenanceMessage || 'The suggestion system is currently under maintenance. Please try again later.',
        'warning'
      );

      await interaction.reply({
        embeds: [maintenanceEmbed],
        flags: MessageFlags.Ephemeral,
      });
      return;
    }

    const categories = config.suggestionCategories || config.settings?.suggestionCategories || [];

    if (categories.length === 0) {
      const noCategories = SuggestionEmbedBuilder.createStatusEmbed(
        'No Categories Available',
        'No suggestion categories are currently configured. Please contact an administrator.',
        'error'
      );

      await interaction.reply({
        embeds: [noCategories],
        flags: MessageFlags.Ephemeral,
      });
      return;
    }

    // Check user suggestion limits
    const member = interaction.member;
    const userSuggestionCount = await SuggestionsUtility.getUserSuggestionCount(
      interaction.user.id, 
      interaction.guildId
    );
    
    const maxSuggestions = suggestionConfig.maxSuggestionsPerUser || CONSTANTS.DEFAULT_MAX_SUGGESTIONS_PER_USER;
    
    if (userSuggestionCount >= maxSuggestions) {
      const limitEmbed = SuggestionEmbedBuilder.createStatusEmbed(
        'Suggestion Limit Reached',
        `You have reached the maximum number of suggestions (${maxSuggestions}). Please wait for some to be processed or contact staff.`,
        'warning'
      );

      await interaction.reply({
        embeds: [limitEmbed],
        flags: MessageFlags.Ephemeral,
      });
      return;
    }

    // Filter categories based on role requirements
    const availableCategories = [];
    for (const category of categories) {
      if (await SuggestionsUtility.hasRequiredRoles(member, category.roleRequirements || [])) {
        availableCategories.push(category);
      }
    }

    if (availableCategories.length === 0) {
      const noAccessEmbed = SuggestionEmbedBuilder.createStatusEmbed(
        'No Accessible Categories',
        'You don\'t have access to any suggestion categories. Contact staff for more information.',
        'warning'
      );

      await interaction.reply({
        embeds: [noAccessEmbed],
        flags: MessageFlags.Ephemeral,
      });
      return;
    }

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('suggestion_category_select')
      .setPlaceholder('Choose a suggestion category')
      .addOptions(
        availableCategories.map((cat) => ({
          label: SuggestionsUtility.truncateText(cat.name, 100),
          value: cat.id,
          description: SuggestionsUtility.truncateText(cat.description || 'No description', 100),
          emoji: cat.emoji || '💡',
        }))
      );

    const row = new ActionRowBuilder().addComponents(selectMenu);

    const infoEmbed = new EmbedBuilder()
      .setTitle('💡 Create a New Suggestion')
      .setDescription('Please select the category that best fits your suggestion. Choose carefully as this will help staff review your suggestion more efficiently.')
      .addFields(
        { 
          name: '📊 Your Stats', 
          value: `Suggestions submitted: ${userSuggestionCount}/${maxSuggestions}`, 
          inline: true 
        },
        { 
          name: '⏰ Global Cooldown', 
          value: `${suggestionConfig.globalCooldownMinutes || CONSTANTS.DEFAULT_COOLDOWN_MINUTES} minutes`, 
          inline: true 
        },
        { 
          name: '📋 Available Categories', 
          value: `${availableCategories.length} categories`, 
          inline: true 
        }
      )
      .setColor(Colors.Blue)
      .setTimestamp();

    await interaction.reply({
      embeds: [infoEmbed],
      components: [row],
      flags: MessageFlags.Ephemeral,
    });

  } catch (error) {
    logger.error('Error in handleCreateSuggestionButton', {
      error: error.message,
      stack: error.stack,
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });

    const errorEmbed = SuggestionEmbedBuilder.createStatusEmbed(
      'Error',
      'An unexpected error occurred while setting up the suggestion form. Please try again later.',
      'error'
    );

    await interaction.reply({
      embeds: [errorEmbed],
      flags: MessageFlags.Ephemeral,
    }).catch(() => {});
  }
}

export async function handleSuggestionCategorySelect(interaction, config) {
  try {
    // Schedule ephemeral deletion
    scheduleEphemeralDeletion(interaction.id, interaction, 120000);

    const categoryValue = interaction.values[0];
    const category = (config.suggestionCategories || config.settings?.suggestionCategories)?.find(
      (cat) => cat.id === categoryValue
    );

    if (!category) {
      const errorEmbed = SuggestionEmbedBuilder.createStatusEmbed(
        'Invalid Category',
        'The selected category is no longer available.',
        'error'
      );

      await interaction.reply({
        embeds: [errorEmbed],
        flags: MessageFlags.Ephemeral,
      });
      return;
    }

    // Enhanced cooldown check
    const cooldownMinutes = category.cooldownMinutes || 
                           config.settings.features?.suggestions?.globalCooldownMinutes || 
                           CONSTANTS.DEFAULT_COOLDOWN_MINUTES;
                           
    const isOnCooldown = await SuggestionsUtility.checkUserCooldown(
      interaction.user.id, 
      categoryValue || 'default', 
      cooldownMinutes
    );
    
    if (isOnCooldown) {
      const cooldownEmbed = SuggestionEmbedBuilder.createStatusEmbed(
        'Cooldown Active',
        `You must wait ${cooldownMinutes} minutes between suggestions in the ${category.name} category.`,
        'warning'
      );

      await interaction.reply({
        embeds: [cooldownEmbed],
        flags: MessageFlags.Ephemeral,
      });
      return;
    }

    const modal = new ModalBuilder()
      .setCustomId(`suggestion_modal_${categoryValue}`)
      .setTitle(`New ${SuggestionsUtility.truncateText(category.name, 40)} Suggestion`);

    const titleInput = new TextInputBuilder()
      .setCustomId('suggestion_title')
      .setLabel('Suggestion Title')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder(`Brief, descriptive title (max ${CONSTANTS.MAX_SUGGESTION_TITLE_LENGTH} chars)`)
      .setRequired(true)
      .setMinLength(CONSTANTS.MIN_SUGGESTION_TITLE_LENGTH)
      .setMaxLength(CONSTANTS.MAX_SUGGESTION_TITLE_LENGTH);

    const descriptionInput = new TextInputBuilder()
      .setCustomId('suggestion_description')
      .setLabel('Detailed Description')
      .setStyle(TextInputStyle.Paragraph)
      .setPlaceholder('Provide a comprehensive description of your suggestion...')
      .setRequired(true)
      .setMinLength(CONSTANTS.MIN_SUGGESTION_DESCRIPTION_LENGTH)
      .setMaxLength(CONSTANTS.MAX_SUGGESTION_DESCRIPTION_LENGTH);

    const reasonInput = new TextInputBuilder()
      .setCustomId('suggestion_reason')
      .setLabel('Why should this be implemented?')
      .setStyle(TextInputStyle.Paragraph)
      .setPlaceholder('Explain the benefits and why this would be valuable...')
      .setRequired(false)
      .setMaxLength(CONSTANTS.MAX_SUGGESTION_REASON_LENGTH);

    const tagsInput = new TextInputBuilder()
      .setCustomId('suggestion_tags')
      .setLabel(`Tags (optional, max ${CONSTANTS.MAX_TAGS_PER_SUGGESTION})`)
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('e.g., quality-of-life, performance, feature')
      .setRequired(false)
      .setMaxLength(CONSTANTS.MAX_SUGGESTION_TAGS_LENGTH);

    const titleRow = new ActionRowBuilder().addComponents(titleInput);
    const descriptionRow = new ActionRowBuilder().addComponents(descriptionInput);
    const reasonRow = new ActionRowBuilder().addComponents(reasonInput);
    const tagsRow = new ActionRowBuilder().addComponents(tagsInput);

    modal.addComponents(titleRow, descriptionRow, reasonRow, tagsRow);

    await interaction.showModal(modal);

  } catch (error) {
    logger.error('Error in handleSuggestionCategorySelect', {
      error: error.message,
      stack: error.stack,
      userId: interaction.user.id,
      guildId: interaction.guildId,
      categoryValue: interaction.values?.[0],
    });

    const errorEmbed = SuggestionEmbedBuilder.createStatusEmbed(
      'Error',
      'Failed to open the suggestion form. Please try again.',
      'error'
    );

    await interaction.reply({
      embeds: [errorEmbed],
      flags: MessageFlags.Ephemeral,
    }).catch(() => {});
  }
}

export async function handleSuggestionModalSubmit(interaction, config) {
  try {
    // Defer the reply immediately
    try {
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });
    } catch (deferError) {
      logger.error('Failed to defer reply', { error: deferError.message });
      // Try to reply instead
      await interaction.reply({ 
        content: 'Processing your suggestion...', 
        flags: MessageFlags.Ephemeral 
      });
    }
    
    const categoryId = interaction.customId.replace('suggestion_modal_', '');

    // Debug logging at the start
    logger.info('handleSuggestionModalSubmit called', {
      categoryId,
      configKeys: Object.keys(config || {}),
      hasConfig: !!config
    });

    // Explicitly load the configuration
    const loadedConfig = await SuggestionsUtility.loadConfig();
    
    // Debug logging after loading config
    logger.info('Config loaded', {
      loadedConfigKeys: Object.keys(loadedConfig || {}),
      hasLoadedConfig: !!loadedConfig
    });

    // Get the category
    const category = (loadedConfig.suggestionCategories || config.suggestionCategories)?.find(
      (cat) => cat.id === categoryId
    );

    if (!category) {
      throw new Error('Invalid suggestion category');
    }

    // Validate inputs
    const title = interaction.fields.getTextInputValue('suggestion_title');
    const description = interaction.fields.getTextInputValue('suggestion_description');
    const reason = interaction.fields.getTextInputValue('suggestion_reason') || '';
    const tagsInput = interaction.fields.getTextInputValue('suggestion_tags') || '';

    // Validate input lengths
    if (title.length < CONSTANTS.MIN_SUGGESTION_TITLE_LENGTH || 
        title.length > CONSTANTS.MAX_SUGGESTION_TITLE_LENGTH) {
      throw new Error(`Suggestion title must be between ${CONSTANTS.MIN_SUGGESTION_TITLE_LENGTH} and ${CONSTANTS.MAX_SUGGESTION_TITLE_LENGTH} characters.`);
    }

    if (description.length < CONSTANTS.MIN_SUGGESTION_DESCRIPTION_LENGTH || 
        description.length > CONSTANTS.MAX_SUGGESTION_DESCRIPTION_LENGTH) {
      throw new Error(`Suggestion description must be between ${CONSTANTS.MIN_SUGGESTION_DESCRIPTION_LENGTH} and ${CONSTANTS.MAX_SUGGESTION_DESCRIPTION_LENGTH} characters.`);
    }

    // Parse tags
    const tags = SuggestionsUtility.parseTags(tagsInput);

    // Generate unique suggestion number with retry logic
    let suggestionNumber;
    let attempts = 0;
    const maxAttempts = 10;
    const suggestionsDb = await getSuggestionsDb();
    
    do {
      suggestionNumber = SuggestionsUtility.generateSuggestionNumber();
      const existing = await suggestionsDb.findOne({ suggestionNumber });
      if (!existing) break;
      attempts++;
    } while (attempts < maxAttempts);

    if (attempts >= maxAttempts) {
      throw new Error('Failed to generate unique suggestion number after multiple attempts');
    }

    // Check for similar suggestions
    const similarSuggestions = await suggestionsDb.find({
      guildId: interaction.guildId,
      title: { $regex: new RegExp(title.substring(0, Math.min(20, title.length)), 'i') },
      status: { $ne: 'rejected' }
    }).limit(5).toArray();

    // Prepare suggestion data
    const suggestionData = {
      suggestionNumber,
      messageId: '', // Will be set after message creation
      threadId: '', // Will be set after thread creation
      channelId: '', // Will be set after message creation
      userId: interaction.user.id,
      username: interaction.user.username,
      displayName: interaction.member?.displayName || interaction.user.globalName || interaction.user.username,
      category: categoryId,
      title,
      description,
      reason,
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date(),
      guildId: interaction.guildId,
      votes: {
        upvotes: 0,
        downvotes: 0,
        neutral: 0,
        voters: [],
        upvotesVoters: [],
        downvotesVoters: [],
        neutralVoters: [],
      },
      tags,
      isAnonymous: false,
      metadata: {
        clientVersion: interaction.client.user.tag,
        submissionMethod: 'modal',
      }
    };

    // Get and validate suggestions channel
    const suggestionConfig = loadedConfig.settings?.features?.tickets?.suggestions || loadedConfig.settings?.features?.suggestions || {};
    const suggestionsChannelId = suggestionConfig.channelId;

    // Debug logging
    logger.info('Suggestion channel configuration', {
      loadedConfigKeys: Object.keys(loadedConfig),
      hasSettings: !!loadedConfig.settings,
      hasFeatures: !!loadedConfig.settings?.features,
      hasTickets: !!loadedConfig.settings?.features?.tickets,
      hasSuggestions: !!loadedConfig.settings?.features?.suggestions,
      hasTicketsSuggestions: !!loadedConfig.settings?.features?.tickets?.suggestions,
      suggestionsChannelId,
      suggestionConfig,
      ticketsSuggestionsChannelId: loadedConfig.settings?.features?.tickets?.suggestions?.channelId,
      featuresSuggestionsChannelId: loadedConfig.settings?.features?.suggestions?.channelId
    });

    if (!suggestionsChannelId) {
      throw new Error('No suggestions channel configured in config.yml');
    }

    const suggestionsChannel = await interaction.guild?.channels.fetch(suggestionsChannelId).catch(() => null);

    if (!suggestionsChannel) {
      throw new Error('Suggestions channel not found');
    }

    if (!SuggestionsUtility.validateChannelType(suggestionsChannel)) {
      throw new Error('Invalid suggestions channel type');
    }

    // Check bot permissions
    const botMember = await interaction.guild?.members.fetch(interaction.client.user.id);
    const missingPermissions = await SuggestionsUtility.checkBotPermissions(suggestionsChannel, botMember);

    if (missingPermissions.length > 0) {
      logger.error('Bot lacks required permissions in suggestions channel', {
        channelId: suggestionsChannelId,
        missingPermissions: missingPermissions.map(perm => perm.toString()),
        userId: interaction.user.id,
        guildId: interaction.guildId,
      });
      throw new Error('Bot lacks required permissions to post suggestions');
    }

    // Create suggestion embed
    const suggestionEmbed = SuggestionEmbedBuilder.createSuggestionEmbed(
      suggestionData, 
      category, 
      suggestionData.isAnonymous
    );

    // Create action buttons
    const actionButtons = SuggestionEmbedBuilder.createActionButtons(
      suggestionNumber, 
      suggestionData.status
    );

    // Post suggestion message
    const suggestionMessage = await suggestionsChannel.send({
      embeds: [suggestionEmbed],
      components: suggestionConfig.enableVoting !== false ? actionButtons : [],
    });

    // Update suggestion data with message info
    suggestionData.messageId = suggestionMessage.id;
    suggestionData.channelId = suggestionMessage.channel.id;

    // Create discussion thread
    let suggestionThread = null;
    try {
      suggestionThread = await suggestionMessage.startThread({
        name: SuggestionsUtility.truncateText(title, 100),
        autoArchiveDuration: CONSTANTS.THREAD_AUTO_ARCHIVE_DURATION,
        reason: `Discussion thread for suggestion #${suggestionNumber}`,
      });

      suggestionData.threadId = suggestionThread.id;

      // Send initial thread message
      const threadWelcomeEmbed = new EmbedBuilder()
        .setTitle('💬 Discussion Thread')
        .setDescription(
          `Welcome to the discussion thread for **${title}**!\n\n` +
          `Feel free to share your thoughts, ask questions, or provide additional feedback about this suggestion.\n\n` +
          `**Guidelines:**\n` +
          `• Keep discussions respectful and constructive\n` +
          `• Stay on topic related to this suggestion\n` +
          `• Avoid duplicate comments - check what others have said first`
        )
        .setColor(Colors.Blue)
        .addFields(
          { name: 'Original Author', value: `<@${suggestionData.userId}>`, inline: true },
          { name: 'Category', value: category.name, inline: true },
          { name: 'Status', value: SuggestionsUtility.capitalizeFirst(suggestionData.status), inline: true }
        )
        .setTimestamp();

      await suggestionThread.send({
        embeds: [threadWelcomeEmbed],
        content: `👋 <@${suggestionData.userId}> your suggestion thread is ready!`
      });

    } catch (threadError) {
      logger.warn('Failed to create suggestion thread', {
        error: threadError.message,
        suggestionNumber,
        messageId: suggestionMessage.id,
      });
    }

    // Insert suggestion into database
    const insertResult = await suggestionsDb.insertOne(suggestionData);
    
    if (!insertResult.acknowledged) {
      throw new Error('Failed to save suggestion to database');
    }

    // Update user cooldown
    SuggestionsUtility.updateUserCooldown(interaction.user.id, categoryId);

    // Invalidate user suggestion count cache
    const cacheKey = `${interaction.guildId}-${interaction.user.id}`;
    userSuggestionCounts.delete(cacheKey);

    // Prepare success response
    let duplicateWarning = '';
    if (similarSuggestions.length > 0) {
      duplicateWarning = `\n\n⚠️ **Similar suggestions found:** ${similarSuggestions.length} suggestion(s) with similar titles exist. Please ensure your suggestion is unique.`;
    }

    const successEmbed = SuggestionEmbedBuilder.createStatusEmbed(
      'Suggestion Submitted Successfully',
      `Your suggestion #${suggestionNumber} has been submitted and is now awaiting review by staff!${duplicateWarning}`,
      'success'
    );

    successEmbed.addFields(
      { name: '🔗 Message Link', value: `[View Suggestion](${suggestionMessage.url})`, inline: true },
      { name: '💬 Discussion', value: suggestionThread ? `[Join Discussion](https://discord.com/channels/${interaction.guildId}/${suggestionThread.id})` : 'Thread creation failed', inline: true },
      { name: '📊 Your Stats', value: `${await SuggestionsUtility.getUserSuggestionCount(interaction.user.id, interaction.guildId)} active suggestions`, inline: true }
    );

    await interaction.editReply({ 
      embeds: [successEmbed],
      flags: MessageFlags.Ephemeral 
    });

    // Log successful suggestion creation
    logger.info('Suggestion created successfully', {
      suggestionNumber,
      userId: interaction.user.id,
      username: interaction.user.username,
      category: categoryId,
      title: title.substring(0, 50),
      messageId: suggestionMessage.id,
      threadId: suggestionThread?.id,
      guildId: interaction.guildId,
    });

    // Send notification to log channel if configured
    await sendLogNotification(interaction, suggestionConfig, {
      type: 'created',
      suggestion: suggestionData,
      category,
      staffMember: null
    });

  } catch (error) {
    logger.error('Failed to create suggestion', {
      error: error.message,
      stack: error.stack,
      userId: interaction.user.id,
      categoryId: interaction.customId.replace('suggestion_modal_', ''),
    });

    const errorEmbed = SuggestionEmbedBuilder.createStatusEmbed(
      'Suggestion Creation Failed',
      `There was an error creating your suggestion: ${error.message}`,
      'error'
    );

    try {
      // Check if interaction has been deferred
      if (interaction.deferred) {
        await interaction.editReply({ 
          embeds: [errorEmbed],
          flags: MessageFlags.Ephemeral 
        });
      } else {
        await interaction.reply({ 
          embeds: [errorEmbed],
          flags: MessageFlags.Ephemeral 
        });
      }
    } catch (replyError) {
      logger.error('Failed to send error reply', {
        replyError: replyError.message,
        originalError: error.message,
        interactionDeferred: interaction.deferred,
        interactionReplied: interaction.replied
      });
    }
  }
}

// Enhanced voting system with better error handling and validation
export async function handleSuggestionVote(interaction, suggestionNumber, voteType) {
  try {
    logger.info('Processing suggestion vote', {
      suggestionNumber,
      voteType,
      userId: interaction.user.id,
      username: interaction.user.username,
      guildId: interaction.guildId
    });

    await interaction.deferReply({ flags: MessageFlags.Ephemeral });

    // Validate vote type
    const validVoteTypes = ['up', 'down', 'neutral'];
    if (!validVoteTypes.includes(voteType)) {
      throw new Error(`Invalid vote type: ${voteType}`);
    }

    // Fetch suggestion with enhanced error handling
    let suggestion;
    try {
      suggestion = await getSuggestionFromCache(suggestionNumber, interaction.guildId);
    } catch (cacheError) {
      logger.warn('Cache miss, fetching from database', { suggestionNumber, error: cacheError.message });
      const suggestionsDb = await getSuggestionsDb();
      suggestion = await suggestionsDb.findOne({ 
        suggestionNumber: parseInt(suggestionNumber), 
        guildId: interaction.guildId 
      });
    }

    if (!suggestion) {
      const errorEmbed = SuggestionEmbedBuilder.createStatusEmbed(
        'Suggestion Not Found',
        `Suggestion #${suggestionNumber} could not be found in this server.`,
        'error'
      );

      await interaction.editReply({ embeds: [errorEmbed] });
      return;
    }

    // Check if voting is allowed for this suggestion status
    if (suggestion.status === 'rejected' || suggestion.status === 'implemented') {
      const statusEmbed = SuggestionEmbedBuilder.createStatusEmbed(
        'Voting Not Allowed',
        `This suggestion has been ${suggestion.status} and voting is no longer allowed.`,
        'warning'
      );

      await interaction.editReply({ embeds: [statusEmbed] });
      return;
    }

    // Check if user is trying to vote on their own suggestion
    if (suggestion.userId === interaction.user.id) {
      const selfVoteEmbed = SuggestionEmbedBuilder.createStatusEmbed(
        'Cannot Vote on Own Suggestion',
        'You cannot vote on your own suggestion.',
        'warning'
      );

      await interaction.editReply({ embeds: [selfVoteEmbed] });
      return;
    }

    // Process vote change
    const voteResult = await processVoteChange(suggestion, interaction.user.id, voteType);
    
    // Update suggestion in database
    const suggestionsDb = await getSuggestionsDb();
    await suggestionsDb.updateOne(
      { suggestionNumber: parseInt(suggestionNumber), guildId: interaction.guildId },
      { 
        $set: { 
          votes: voteResult.updatedVotes,
          updatedAt: new Date()
        } 
      }
    );

    // Queue cache update
    queueSuggestionUpdate(suggestionNumber, interaction.guildId, { 
      votes: voteResult.updatedVotes,
      updatedAt: new Date()
    });

    // Update the original suggestion message
    await updateSuggestionMessage(suggestion, voteResult.updatedVotes, interaction);

    // Create success response
    const voteLabels = { 
      up: { action: 'Upvoted', emoji: '👍', color: Colors.Green },
      down: { action: 'Downvoted', emoji: '👎', color: Colors.Red },
      neutral: { action: 'Neutral Vote', emoji: '🤷', color: Colors.Grey }
    };

    const voteInfo = voteLabels[voteType];
    const successEmbed = new EmbedBuilder()
      .setTitle(`${voteInfo.emoji} Vote Recorded`)
      .setDescription(
        voteResult.wasChanged 
          ? `You changed your vote to **${voteInfo.action}** for suggestion #${suggestionNumber}!`
          : `You have successfully **${voteInfo.action.toLowerCase()}** suggestion #${suggestionNumber}!`
      )
      .addFields(
        { 
          name: '📊 Current Votes', 
          value: `👍 ${voteResult.updatedVotes.upvotes || 0} • 👎 ${voteResult.updatedVotes.downvotes || 0} • 🤷 ${voteResult.updatedVotes.neutral || 0}`, 
          inline: false 
        },
        { 
          name: '🗳️ Total Voters', 
          value: voteResult.updatedVotes.voters.length.toString(), 
          inline: true 
        },
        { 
          name: '📈 Vote Score', 
          value: ((voteResult.updatedVotes.upvotes || 0) - (voteResult.updatedVotes.downvotes || 0)).toString(), 
          inline: true 
        }
      )
      .setColor(voteInfo.color)
      .setTimestamp();

    await interaction.editReply({ embeds: [successEmbed] });

    logger.info('Vote recorded successfully', {
      suggestionNumber,
      userId: interaction.user.id,
      voteType,
      wasChanged: voteResult.wasChanged,
      totalVotes: voteResult.updatedVotes.voters.length,
      voteScore: (voteResult.updatedVotes.upvotes || 0) - (voteResult.updatedVotes.downvotes || 0),
    });

  } catch (error) {
    logger.error('Error in handleSuggestionVote', {
      error: error.message,
      stack: error.stack,
      suggestionNumber,
      voteType,
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });

    const errorEmbed = SuggestionEmbedBuilder.createStatusEmbed(
      'Vote Failed',
      `An error occurred while processing your vote: ${error.message}`,
      'error'
    );

    try {
      await interaction.editReply({ embeds: [errorEmbed] });
    } catch (replyError) {
      logger.error('Failed to send vote error reply', { error: replyError.message });
    }
  }
}

// Helper function to process vote changes
async function processVoteChange(suggestion, userId, voteType) {
  const updatedVotes = JSON.parse(JSON.stringify(suggestion.votes)); // Deep clone
  let wasChanged = false;
  
  // Initialize vote arrays if they don't exist
  updatedVotes.upvotesVoters = updatedVotes.upvotesVoters || [];
  updatedVotes.downvotesVoters = updatedVotes.downvotesVoters || [];
  updatedVotes.neutralVoters = updatedVotes.neutralVoters || [];
  updatedVotes.voters = updatedVotes.voters || [];

  // Remove any existing vote from this user
  const voteTypes = ['upvotesVoters', 'downvotesVoters', 'neutralVoters'];
  for (const type of voteTypes) {
    const index = updatedVotes[type].indexOf(userId);
    if (index !== -1) {
      wasChanged = true;
      updatedVotes[type].splice(index, 1);
      
      // Decrease the corresponding count
      const countKey = type.replace('Voters', '');
      updatedVotes[countKey] = Math.max(0, (updatedVotes[countKey] || 0) - 1);
      
      // Remove from general voters array
      const votersIndex = updatedVotes.voters.indexOf(userId);
      if (votersIndex !== -1) {
        updatedVotes.voters.splice(votersIndex, 1);
      }
    }
  }

  // Add the new vote
  const voteMapping = {
    up: { array: 'upvotesVoters', count: 'upvotes' },
    down: { array: 'downvotesVoters', count: 'downvotes' },
    neutral: { array: 'neutralVoters', count: 'neutral' }
  };

  const mapping = voteMapping[voteType];
  if (mapping) {
    updatedVotes[mapping.array].push(userId);
    updatedVotes[mapping.count] = (updatedVotes[mapping.count] || 0) + 1;
    
    // Add to general voters array if not already present
    if (!updatedVotes.voters.includes(userId)) {
      updatedVotes.voters.push(userId);
    }
  }

  return { updatedVotes, wasChanged };
}

// Helper function to update suggestion message
async function updateSuggestionMessage(suggestion, updatedVotes, interaction) {
  try {
    logger.info('Updating suggestion message', {
      suggestionNumber: suggestion.suggestionNumber,
      channelId: suggestion.channelId,
      messageId: suggestion.messageId,
      hasClient: !!interaction.client
    });

    const config = await SuggestionsUtility.loadConfig();
    const category = (config.suggestionCategories || config.settings?.suggestionCategories)?.find(
      (cat) => cat.id === suggestion.category
    );

    if (!category) {
      logger.warn('Category not found for suggestion update', { 
        suggestionNumber: suggestion.suggestionNumber,
        categoryId: suggestion.category 
      });
      return;
    }

    // Find the channel and message using the interaction's client
    const channel = interaction.client.channels.cache.get(suggestion.channelId) ||
                   await interaction.client.channels.fetch(suggestion.channelId).catch(() => null);

    if (!channel) {
      logger.warn('Channel not found for suggestion update', { 
        suggestionNumber: suggestion.suggestionNumber,
        channelId: suggestion.channelId,
        availableChannels: Array.from(interaction.client.channels.cache.keys())
      });
      return;
    }

    const message = await channel.messages.fetch(suggestion.messageId).catch(() => null);
    if (!message) {
      logger.warn('Message not found for suggestion update', { 
        suggestionNumber: suggestion.suggestionNumber,
        messageId: suggestion.messageId 
      });
      return;
    }

    // Update the embed with new vote counts
    const updatedSuggestion = { ...suggestion, votes: updatedVotes };
    const updatedEmbed = SuggestionEmbedBuilder.createSuggestionEmbed(
      updatedSuggestion, 
      category, 
      suggestion.isAnonymous
    );
    
    const suggestionConfig = config.settings?.features?.tickets?.suggestions || config.settings?.features?.suggestions || {};
    const actionButtons = SuggestionEmbedBuilder.createActionButtons(
      suggestion.suggestionNumber, 
      suggestion.status
    );

    await message.edit({ 
      embeds: [updatedEmbed], 
      components: suggestionConfig.enableVoting !== false ? actionButtons : []
    });

    logger.info('Suggestion message updated successfully', {
      suggestionNumber: suggestion.suggestionNumber,
      channelId: suggestion.channelId,
      messageId: suggestion.messageId
    });

  } catch (error) {
    logger.warn('Failed to update suggestion message after vote', {
      error: error.message,
      suggestionNumber: suggestion.suggestionNumber,
    });
  }
}

// Enhanced status change system
export async function handleSuggestionStatusChange(interaction, suggestionNumber, newStatus) {
  try {
    // For rejection, show a modal to get the reason
    if (newStatus === 'rejected') {
      const modal = new ModalBuilder()
        .setCustomId(`suggestion_reject_modal_${suggestionNumber}`)
        .setTitle(`Reject Suggestion #${suggestionNumber}`);

      const reasonInput = new TextInputBuilder()
        .setCustomId('rejection_reason')
        .setLabel('Reason for Rejection')
        .setStyle(TextInputStyle.Paragraph)
        .setPlaceholder('Please provide a clear reason for rejecting this suggestion...')
        .setRequired(true)
        .setMinLength(CONSTANTS.MIN_REJECTION_REASON_LENGTH)
        .setMaxLength(CONSTANTS.MAX_REJECTION_REASON_LENGTH);

      const reasonRow = new ActionRowBuilder().addComponents(reasonInput);
      modal.addComponents(reasonRow);

      await interaction.showModal(modal);
      return;
    }

    // For other statuses, proceed directly
    await processSuggestionStatusChange(interaction, suggestionNumber, newStatus);

  } catch (error) {
    logger.error('Error handling suggestion status change', {
      error: error.message,
      stack: error.stack,
      suggestionNumber,
      newStatus,
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });

    const errorEmbed = SuggestionEmbedBuilder.createStatusEmbed(
      'Status Update Failed',
      `Failed to update suggestion status: ${error.message}`,
      'error'
    );

    await interaction.reply({ 
      embeds: [errorEmbed], 
      flags: MessageFlags.Ephemeral 
    }).catch(() => {});
  }
}

// Handle rejection modal submission
export async function handleSuggestionRejectModal(interaction, suggestionNumber) {
  try {
    await interaction.deferReply({ flags: MessageFlags.Ephemeral });

    const rejectionReason = SuggestionsUtility.sanitizeInput(
      interaction.fields.getTextInputValue('rejection_reason'),
      CONSTANTS.MAX_REJECTION_REASON_LENGTH
    );
    
    if (!rejectionReason || rejectionReason.length < CONSTANTS.MIN_REJECTION_REASON_LENGTH) {
      throw new Error(`Rejection reason must be at least ${CONSTANTS.MIN_REJECTION_REASON_LENGTH} characters long`);
    }
    
    // Process the rejection with the reason
    await processSuggestionStatusChange(interaction, suggestionNumber, 'rejected', rejectionReason);

  } catch (error) {
    logger.error('Error handling suggestion reject modal', {
      error: error.message,
      stack: error.stack,
      suggestionNumber,
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });

    const errorEmbed = SuggestionEmbedBuilder.createStatusEmbed(
      'Rejection Failed',
      `Failed to reject suggestion: ${error.message}`,
      'error'
    );

    await interaction.editReply({ embeds: [errorEmbed] }).catch(() => {});
  }
}

// Enhanced status change processing
async function processSuggestionStatusChange(interaction, suggestionNumber, newStatus, reason) {
  try {
    if (!interaction.deferred && !interaction.replied) {
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });
    }

    // Validate status
    if (!STATUS_CONFIG[newStatus]) {
      throw new Error(`Invalid status: ${newStatus}`);
    }

    // Load config and check permissions
    const config = await SuggestionsUtility.loadConfig();
    const suggestionConfig = config.settings?.features?.suggestions || {};
    const member = interaction.member;
    
    // Enhanced permission checking
    const hasPermission = await checkStaffPermissions(member, suggestionConfig, interaction.guildId);
    
    if (!hasPermission) {
      const noPermEmbed = SuggestionEmbedBuilder.createStatusEmbed(
        'Insufficient Permissions',
        'You do not have permission to manage suggestions.',
        'error'
      );

      await interaction.editReply({ embeds: [noPermEmbed] });
      return;
    }

    // Fetch and validate suggestion
    const suggestionsDb = await getSuggestionsDb();
    const suggestion = await suggestionsDb.findOne({ 
      suggestionNumber: parseInt(suggestionNumber),
      guildId: interaction.guildId 
    });

    if (!suggestion) {
      const errorEmbed = SuggestionEmbedBuilder.createStatusEmbed(
        'Suggestion Not Found',
        `Suggestion #${suggestionNumber} could not be found.`,
        'error'
      );

      await interaction.editReply({ embeds: [errorEmbed] });
      return;
    }

    // Prevent status change if already finalized
    if ((suggestion.status === 'rejected' || suggestion.status === 'implemented') && 
        (newStatus !== suggestion.status)) {
      const finalizedEmbed = SuggestionEmbedBuilder.createStatusEmbed(
        'Cannot Change Status',
        `This suggestion has already been ${suggestion.status} and cannot be changed.`,
        'warning'
      );

      await interaction.editReply({ embeds: [finalizedEmbed] });
      return;
    }

    // Prepare update data
    const updateData = {
      status: newStatus,
      updatedAt: new Date(),
      lastModifiedBy: interaction.user.id,
    };

    if (newStatus === 'implemented') {
      updateData.implementationDate = new Date();
    }

    if (newStatus === 'rejected' && reason) {
      updateData.rejectionReason = reason;
      updateData.rejectedBy = interaction.user.id;
      updateData.rejectedAt = new Date();
    }

    // Update database
    const updateResult = await suggestionsDb.updateOne(
      { suggestionNumber: parseInt(suggestionNumber), guildId: interaction.guildId },
      { $set: updateData }
    );

    if (!updateResult.acknowledged) {
      throw new Error('Failed to update suggestion in database');
    }

    // Update original message and thread
    await updateSuggestionAfterStatusChange(suggestion, newStatus, reason, interaction, config);

    // Create success response
    const statusInfo = STATUS_CONFIG[newStatus];
    const successEmbed = new EmbedBuilder()
      .setTitle(`${statusInfo.emoji} Status Updated`)
      .setDescription(`Suggestion #${suggestionNumber} has been marked as **${statusInfo.label}**.`)
      .setColor(statusInfo.color)
      .addFields(
        { name: 'Previous Status', value: SuggestionsUtility.capitalizeFirst(suggestion.status), inline: true },
        { name: 'New Status', value: statusInfo.label, inline: true },
        { name: 'Changed By', value: `<@${interaction.user.id}>`, inline: true }
      )
      .setTimestamp();

    if (newStatus === 'rejected' && reason) {
      successEmbed.addFields({ 
        name: '❌ Rejection Reason', 
        value: SuggestionsUtility.truncateText(reason, 1024), 
        inline: false 
      });
    }

    await interaction.editReply({ embeds: [successEmbed] });

    // Send log notification
    await sendLogNotification(interaction, suggestionConfig, {
      type: 'statusChanged',
      suggestion: { ...suggestion, ...updateData },
      category: (config.suggestionCategories || config.settings?.suggestionCategories)?.find(
        cat => cat.id === suggestion.category
      ),
      staffMember: interaction.user,
      previousStatus: suggestion.status,
      newStatus,
      reason
    });

    // Invalidate cache
    invalidateSuggestionCache(suggestionNumber, interaction.guildId);

    logger.info('Suggestion status updated successfully', {
      suggestionNumber,
      previousStatus: suggestion.status,
      newStatus,
      staffUserId: interaction.user.id,
      rejectionReason: reason,
      guildId: interaction.guildId,
    });

  } catch (error) {
    logger.error('Error in processSuggestionStatusChange', {
      error: error.message,
      stack: error.stack,
      suggestionNumber,
      newStatus,
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });

    const errorEmbed = SuggestionEmbedBuilder.createStatusEmbed(
      'Status Update Failed',
      `Failed to update suggestion status: ${error.message}`,
      'error'
    );

    try {
      if (interaction.deferred || interaction.replied) {
        await interaction.editReply({ embeds: [errorEmbed] });
      } else {
        await interaction.reply({ embeds: [errorEmbed], flags: MessageFlags.Ephemeral });
      }
    } catch (replyError) {
      logger.error('Failed to send status change error reply', { error: replyError.message });
    }
  }
}

// Helper function to check staff permissions
async function checkStaffPermissions(member, suggestionConfig, guildId) {
  try {
    // Check for administrator permission
    if (member.permissions?.has(PermissionsBitField.Flags.Administrator)) {
      return true;
    }

    // Check for configured staff roles
    const staffRoles = suggestionConfig.staffRoleIds || [];
    const validStaffRoles = staffRoles.filter(roleId => 
      roleId && 
      roleId !== guildId && // Exclude @everyone role
      roleId.length > 0
    );
    
    return validStaffRoles.some(roleId => member.roles?.cache?.has(roleId));
  } catch (error) {
    logger.error('Error checking staff permissions', { error: error.message, memberId: member?.id });
    return false;
  }
}

// Helper function to update suggestion message and thread after status change
async function updateSuggestionAfterStatusChange(suggestion, newStatus, reason, interaction, config) {
  try {
    const category = (config.suggestionCategories || config.settings?.suggestionCategories)?.find(
      cat => cat.id === suggestion.category
    );

    if (!category) {
      logger.warn('Category not found for status change update', { 
        suggestionNumber: suggestion.suggestionNumber,
        categoryId: suggestion.category 
      });
      return;
    }

    // Update the original message
    const channel = await interaction.client.channels.fetch(suggestion.channelId).catch(() => null);
    if (channel) {
      const message = await channel.messages.fetch(suggestion.messageId).catch(() => null);
      if (message) {
        const updatedSuggestion = { 
          ...suggestion, 
          status: newStatus,
          rejectionReason: reason,
          rejectedBy: interaction.user.id,
          rejectedAt: new Date()
        };

        const updatedEmbed = SuggestionEmbedBuilder.createSuggestionEmbed(
          updatedSuggestion, 
          category, 
          suggestion.isAnonymous
        );

        const suggestionConfig = config.settings?.features?.suggestions || {};
        
        // Remove buttons for finalized suggestions
        const actionButtons = (newStatus === 'rejected' || newStatus === 'implemented') 
          ? [] 
          : SuggestionEmbedBuilder.createActionButtons(suggestion.suggestionNumber, newStatus);

        await message.edit({ 
          embeds: [updatedEmbed], 
          components: suggestionConfig.enableVoting !== false ? actionButtons : []
        });
      }
    }

    // Update the thread if it exists
    if (suggestion.threadId) {
      const thread = await interaction.client.channels.fetch(suggestion.threadId).catch(() => null);
      if (thread) {
        const statusInfo = STATUS_CONFIG[newStatus];
        const statusUpdateEmbed = new EmbedBuilder()
          .setTitle(`${statusInfo.emoji} Status Update: ${statusInfo.label}`)
          .setDescription(statusInfo.description)
          .setColor(statusInfo.color)
          .addFields(
            { name: 'Updated By', value: `<@${interaction.user.id}>`, inline: true },
            { name: 'Original Suggestion', value: SuggestionsUtility.truncateText(suggestion.title, 100), inline: true },
            { name: 'Updated', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
          )
          .setTimestamp();

        if (newStatus === 'rejected' && reason) {
          statusUpdateEmbed.addFields({ 
            name: '❌ Rejection Reason', 
            value: SuggestionsUtility.truncateText(reason, 1024), 
            inline: false 
          });
        }

        await thread.send({ 
          embeds: [statusUpdateEmbed],
          content: `🔔 <@${suggestion.userId}> Status Update`
        });

        // Lock thread for finalized suggestions
        if (newStatus === 'rejected' || newStatus === 'implemented') {
          try {
            await thread.setLocked(true, `Suggestion ${newStatus} - thread locked`);
            await thread.setArchived(true, `Suggestion ${newStatus} - thread archived`);
          } catch (lockError) {
            logger.warn('Failed to lock/archive suggestion thread', {
              error: lockError.message,
              threadId: thread.id,
              suggestionNumber: suggestion.suggestionNumber,
            });
          }
        }
      }
    }

  } catch (error) {
    logger.error('Error updating suggestion after status change', {
      error: error.message,
      stack: error.stack,
      suggestionNumber: suggestion.suggestionNumber,
      newStatus
    });
  }
}

// Helper function to send log notifications
async function sendLogNotification(interaction, suggestionConfig, eventData) {
  if (!suggestionConfig.logChannelId || !suggestionConfig.notificationSettings?.notifyOnStatusChange) {
    return;
  }

  try {
    const logChannel = await interaction.client.channels.fetch(suggestionConfig.logChannelId).catch(() => null);
    if (!logChannel || !SuggestionsUtility.validateChannelType(logChannel)) {
      return;
    }

    const { type, suggestion, category, staffMember, previousStatus, newStatus, reason } = eventData;

    let logEmbed;

    if (type === 'created') {
      logEmbed = new EmbedBuilder()
        .setTitle('📝 New Suggestion Created')
        .setDescription(`**${suggestion.title}**`)
        .addFields(
          { name: 'Suggestion ID', value: `#${suggestion.suggestionNumber}`, inline: true },
          { name: 'Author', value: suggestion.isAnonymous ? 'Anonymous' : `<@${suggestion.userId}>`, inline: true },
          { name: 'Category', value: category?.name || 'Unknown', inline: true },
          { name: 'Status', value: SuggestionsUtility.capitalizeFirst(suggestion.status), inline: true },
          { name: 'Created', value: `<t:${Math.floor(suggestion.createdAt.getTime() / 1000)}:F>`, inline: true }
        )
        .setColor(Colors.Blue)
        .setTimestamp();

      if (suggestion.tags && suggestion.tags.length > 0) {
        logEmbed.addFields({ 
          name: '🏷️ Tags', 
          value: suggestion.tags.map(tag => `\`${tag}\``).join(' '), 
          inline: false 
        });
      }
    } else if (type === 'statusChanged') {
      const statusInfo = STATUS_CONFIG[newStatus];
      logEmbed = new EmbedBuilder()
        .setTitle(`${statusInfo.emoji} Suggestion Status Changed`)
        .setDescription(`**${suggestion.title}**`)
        .addFields(
          { name: 'Suggestion ID', value: `#${suggestion.suggestionNumber}`, inline: true },
          { name: 'Author', value: suggestion.isAnonymous ? 'Anonymous' : `<@${suggestion.userId}>`, inline: true },
          { name: 'Staff Member', value: `<@${staffMember.id}>`, inline: true },
          { name: 'Previous Status', value: SuggestionsUtility.capitalizeFirst(previousStatus), inline: true },
          { name: 'New Status', value: statusInfo.label, inline: true },
          { name: 'Votes', value: `👍 ${suggestion.votes?.upvotes || 0} • 👎 ${suggestion.votes?.downvotes || 0} • 🤷 ${suggestion.votes?.neutral || 0}`, inline: true }
        )
        .setColor(statusInfo.color)
        .setTimestamp();

      if (newStatus === 'rejected' && reason) {
        logEmbed.addFields({ 
          name: '❌ Rejection Reason', 
          value: SuggestionsUtility.truncateText(reason, 1024), 
          inline: false 
        });
      }
    }

    if (logEmbed) {
      await logChannel.send({ embeds: [logEmbed] });
    }

  } catch (error) {
    logger.warn('Failed to send log notification', {
      error: error.message,
      type: eventData.type,
      suggestionNumber: eventData.suggestion?.suggestionNumber
    });
  }
}

// Enhanced suggestion selection and management
export async function handleSuggestionSelect(interaction) {
  try {
    const suggestionId = parseInt(interaction.values[0]);
    const suggestionsDb = await getSuggestionsDb();
    
    const suggestion = await suggestionsDb.findOne({ 
      suggestionNumber: suggestionId,
      guildId: interaction.guildId 
    });

    if (!suggestion) {
      const errorEmbed = SuggestionEmbedBuilder.createStatusEmbed(
        'Suggestion Not Found',
        `Suggestion #${suggestionId} could not be found.`,
        'error'
      );

      await interaction.update({
        embeds: [errorEmbed],
        components: [],
      });
      return;
    }

    // Load config to get category info
    const config = await SuggestionsUtility.loadConfig();
    const category = (config.suggestionCategories || config.settings?.suggestionCategories)?.find(
      cat => cat.id === suggestion.category
    );

    const totalVotes = (suggestion.votes?.upvotes || 0) + (suggestion.votes?.downvotes || 0) + (suggestion.votes?.neutral || 0);
    const voteScore = (suggestion.votes?.upvotes || 0) - (suggestion.votes?.downvotes || 0);
    const statusInfo = STATUS_CONFIG[suggestion.status] || STATUS_CONFIG.pending;
    
    const embed = new EmbedBuilder()
      .setTitle(`${statusInfo.emoji} Suggestion #${suggestion.suggestionNumber}`)
      .setDescription(SuggestionsUtility.truncateText(suggestion.description, 2048))
      .addFields(
        { name: '📝 Title', value: suggestion.title, inline: false },
        { name: '📊 Status', value: statusInfo.label, inline: true },
        { name: '👤 Author', value: suggestion.isAnonymous ? 'Anonymous' : `<@${suggestion.userId}>`, inline: true },
        { name: '📂 Category', value: category?.name || 'Unknown', inline: true },
        { name: '🗳️ Votes', value: `👍 ${suggestion.votes?.upvotes || 0} 👎 ${suggestion.votes?.downvotes || 0} 🤷 ${suggestion.votes?.neutral || 0}`, inline: true },
        { name: '📈 Vote Score', value: voteScore.toString(), inline: true },
        { name: '👥 Total Voters', value: totalVotes.toString(), inline: true },
        { name: '📅 Created', value: `<t:${Math.floor(suggestion.createdAt.getTime() / 1000)}:F>`, inline: true },
        { name: '🔄 Updated', value: `<t:${Math.floor((suggestion.updatedAt || suggestion.createdAt).getTime() / 1000)}:R>`, inline: true }
      )
      .setColor(statusInfo.color)
      .setTimestamp();

    // Add additional fields based on status
    if (suggestion.reason && suggestion.reason.trim() !== 'No additional reasoning provided.') {
      embed.addFields({ 
        name: '💭 Reasoning', 
        value: SuggestionsUtility.truncateText(suggestion.reason, 1024), 
        inline: false 
      });
    }

    if (suggestion.tags && suggestion.tags.length > 0) {
      embed.addFields({ 
        name: '🏷️ Tags', 
        value: suggestion.tags.map(tag => `\`${tag}\``).join(' '), 
        inline: false 
      });
    }

    if (suggestion.status === 'rejected') {
      if (suggestion.rejectionReason) {
        embed.addFields({ 
          name: '❌ Rejection Reason', 
          value: SuggestionsUtility.truncateText(suggestion.rejectionReason, 1024), 
          inline: false 
        });
      }
      if (suggestion.rejectedBy) {
        embed.addFields({ 
          name: '👤 Rejected By', 
          value: `<@${suggestion.rejectedBy}>`, 
          inline: true 
        });
      }
      if (suggestion.rejectedAt) {
        embed.addFields({ 
          name: '📅 Rejected On', 
          value: `<t:${Math.floor(suggestion.rejectedAt.getTime() / 1000)}:F>`, 
          inline: true 
        });
      }
    }

    if (suggestion.status === 'implemented' && suggestion.implementationDate) {
      embed.addFields({ 
        name: '🚀 Implemented On', 
        value: `<t:${Math.floor(suggestion.implementationDate.getTime() / 1000)}:F>`, 
        inline: true 
      });
    }

    // Create management action buttons (only for non-finalized suggestions)
    const actionButtons = [];
    if (suggestion.status !== 'rejected' && suggestion.status !== 'implemented') {
      actionButtons.push(
        new ActionRowBuilder()
          .addComponents(
            new ButtonBuilder()
              .setCustomId(`suggestion_approve_${suggestion.suggestionNumber}`)
              .setLabel('Approve')
              .setEmoji('✅')
              .setStyle(ButtonStyle.Success),
            new ButtonBuilder()
              .setCustomId(`suggestion_reject_${suggestion.suggestionNumber}`)
              .setLabel('Reject')
              .setEmoji('❌')
              .setStyle(ButtonStyle.Danger),
            new ButtonBuilder()
              .setCustomId(`suggestion_consider_${suggestion.suggestionNumber}`)
              .setLabel('Consider')
              .setEmoji('🤔')
              .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
              .setCustomId(`suggestion_implement_${suggestion.suggestionNumber}`)
              .setLabel('Implement')
              .setEmoji('🚀')
              .setStyle(ButtonStyle.Success)
          )
      );
    }

    // Create utility buttons
    const utilityButtons = new ActionRowBuilder();
    
    // Back to list button
    utilityButtons.addComponents(
      new ButtonBuilder()
        .setCustomId('suggestions_back')
        .setLabel('Back to List')
        .setEmoji('📋')
        .setStyle(ButtonStyle.Secondary)
    );

    // View original message button (if message still exists)
    if (suggestion.messageId && suggestion.channelId) {
      utilityButtons.addComponents(
        new ButtonBuilder()
          .setLabel('View Original')
          .setEmoji('🔗')
          .setStyle(ButtonStyle.Link)
          .setURL(`https://discord.com/channels/${interaction.guildId}/${suggestion.channelId}/${suggestion.messageId}`)
      );
    }

    // View discussion thread button (if thread exists)
    if (suggestion.threadId) {
      utilityButtons.addComponents(
        new ButtonBuilder()
          .setLabel('Discussion Thread')
          .setEmoji('💬')
          .setStyle(ButtonStyle.Link)
          .setURL(`https://discord.com/channels/${interaction.guildId}/${suggestion.threadId}`)
      );
    }

    const components = [...actionButtons, utilityButtons];

    await interaction.update({ 
      embeds: [embed], 
      components: components 
    });

  } catch (error) {
    logger.error('Error handling suggestion select', {
      error: error.message,
      stack: error.stack,
      userId: interaction.user.id,
      guildId: interaction.guildId,
      suggestionId: interaction.values?.[0]
    });

    const errorEmbed = SuggestionEmbedBuilder.createStatusEmbed(
      'Error Loading Suggestion',
      'An error occurred while loading the suggestion details.',
      'error'
    );

    await interaction.update({
      embeds: [errorEmbed],
      components: [],
    });
  }
}

// Enhanced back button handling with pagination
export async function handleSuggestionsBack(interaction, page = 0) {
  try {
    const suggestionsDb = await getSuggestionsDb();
    const pageSize = 25;
    const skip = page * pageSize;

    // Get total count for pagination
    const totalSuggestions = await suggestionsDb.countDocuments({ 
      guildId: interaction.guildId 
    });

    // Get suggestions with pagination
    const suggestions = await suggestionsDb
      .find({ guildId: interaction.guildId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(pageSize)
      .toArray();

    if (suggestions.length === 0) {
      const embed = SuggestionEmbedBuilder.createStatusEmbed(
        'No Suggestions Found',
        'No suggestions found in this server.',
        'warning'
      );

      await interaction.update({ embeds: [embed], components: [] });
      return;
    }

    // Filter suggestions by status for stats
    const activeSuggestions = suggestions.filter(s => s.status !== 'rejected');
    const statusCounts = {
      pending: suggestions.filter(s => s.status === 'pending').length,
      approved: suggestions.filter(s => s.status === 'approved').length,
      considering: suggestions.filter(s => s.status === 'considering').length,
      implemented: suggestions.filter(s => s.status === 'implemented').length,
      rejected: suggestions.filter(s => s.status === 'rejected').length,
    };

    // Create select menu for suggestions (max 25 options)
    const selectOptions = suggestions.map(suggestion => {
      const statusInfo = STATUS_CONFIG[suggestion.status] || STATUS_CONFIG.pending;
      const totalVotes = (suggestion.votes?.upvotes || 0) + (suggestion.votes?.downvotes || 0) + (suggestion.votes?.neutral || 0);
      
      return {
        label: SuggestionsUtility.truncateText(`#${suggestion.suggestionNumber} - ${suggestion.title}`, 100),
        value: suggestion.suggestionNumber.toString(),
        description: SuggestionsUtility.truncateText(`${statusInfo.label} • ${totalVotes} votes`, 100),
        emoji: statusInfo.emoji,
      };
    });

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('suggestion_select')
      .setPlaceholder('Select a suggestion to manage')
      .addOptions(selectOptions);

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);

    // Create pagination buttons if needed
    const components = [selectRow];
    const totalPages = Math.ceil(totalSuggestions / pageSize);
    
    if (totalPages > 1) {
      const paginationRow = new ActionRowBuilder();
      
      // Previous page button
      paginationRow.addComponents(
        new ButtonBuilder()
          .setCustomId(`suggestions_page_${Math.max(0, page - 1)}`)
          .setLabel('Previous')
          .setEmoji('◀️')
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(page === 0)
      );

      // Page indicator
      paginationRow.addComponents(
        new ButtonBuilder()
          .setCustomId('suggestions_page_info')
          .setLabel(`Page ${page + 1}/${totalPages}`)
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(true)
      );

      // Next page button
      paginationRow.addComponents(
        new ButtonBuilder()
          .setCustomId(`suggestions_page_${Math.min(totalPages - 1, page + 1)}`)
          .setLabel('Next')
          .setEmoji('▶️')
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(page >= totalPages - 1)
      );

      components.push(paginationRow);
    }

    const embed = new EmbedBuilder()
      .setTitle('📋 Suggestion Management')
      .setDescription(`Manage suggestions in this server. Select a suggestion from the menu below to view details and change its status.\n\n**Page ${page + 1} of ${totalPages}** (${totalSuggestions} total suggestions)`)
      .addFields(
        { name: '⏳ Pending', value: statusCounts.pending.toString(), inline: true },
        { name: '✅ Approved', value: statusCounts.approved.toString(), inline: true },
        { name: '🤔 Considering', value: statusCounts.considering.toString(), inline: true },
        { name: '🚀 Implemented', value: statusCounts.implemented.toString(), inline: true },
        { name: '❌ Rejected', value: statusCounts.rejected.toString(), inline: true },
        { name: '📊 Active', value: activeSuggestions.length.toString(), inline: true }
      )
      .setColor(Colors.Blue)
      .setTimestamp()
      .setFooter({ text: `Showing ${suggestions.length} suggestions on this page` });

    await interaction.update({ 
      embeds: [embed], 
      components: components
    });

  } catch (error) {
    logger.error('Error handling suggestions back', {
      error: error.message,
      stack: error.stack,
      userId: interaction.user.id,
      guildId: interaction.guildId,
      page
    });

    const errorEmbed = SuggestionEmbedBuilder.createStatusEmbed(
      'Error Loading Suggestions',
      'An error occurred while loading the suggestions list.',
      'error'
    );

    await interaction.update({
      embeds: [errorEmbed],
      components: [],
    });
  }
}

// Handle pagination
export async function handleSuggestionsPagination(interaction, page) {
  await handleSuggestionsBack(interaction, parseInt(page));
}

// Cleanup functions
function startCleanupTasks() {
  // Clean up expired cooldowns
  setInterval(() => {
    const now = Date.now();
    for (const [userId, cooldowns] of userCooldowns.entries()) {
      const activeCooldowns = cooldowns.filter(c => {
        const expiry = c.lastSubmission.getTime() + (5 * 60 * 1000); // Default 5 minutes
        return now < expiry;
      });
      
      if (activeCooldowns.length === 0) {
        userCooldowns.delete(userId);
      } else if (activeCooldowns.length !== cooldowns.length) {
        userCooldowns.set(userId, activeCooldowns);
      }
    }
  }, COOLDOWN_CLEANUP_INTERVAL);

  // Clean up cache entries
  setInterval(() => {
    const now = Date.now();
    
    // Clean suggestion count cache
    for (const [key, data] of userSuggestionCounts.entries()) {
      if (now - data.timestamp > CACHE_TTL) {
        userSuggestionCounts.delete(key);
      }
    }
    
    // Clean config cache
    for (const [key, data] of configCache.entries()) {
      if (now - data.timestamp > CACHE_TTL) {
        configCache.delete(key);
      }
    }
  }, CACHE_TTL);
}

// Initialize cleanup tasks
startCleanupTasks();

// Export utility functions and classes
export { 
  SuggestionsUtility,
  SuggestionEmbedBuilder,
  STATUS_CONFIG,
  CONSTANTS,
  startCleanupTasks
};