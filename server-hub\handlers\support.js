import { 
  EmbedBuilder, 
  MessageFlags, 
  StringSelectMenuBuilder, 
  ActionRowBuilder 
} from 'discord.js';
import Logger from '../../../utils/logger.js';
import { updateEphemeralStatus } from '../utils/ephemeralUtils.js';
import { scheduleEphemeralDeletion } from '../utils/ephemeralUtils.js';
import { isWithinWorkingHours, formatWorkingHoursStatus } from '../utils/workingHoursUtils.js';

// Create a logger instance
const logger = new Logger({ level: 'info' });

export async function handleGetSupportButton(interaction, config) {
  const features = config.settings.features || {};

  // Check working hours for tickets
  const workingHoursStatus = isWithinWorkingHours(config);
  const statusDisplay = formatWorkingHoursStatus(workingHoursStatus, config);

  const supportOptions = [];

  // Always show ticket option if tickets are enabled
  // Emergency categories can be created even outside working hours
  if (features.tickets?.enabled !== false) {
    let ticketDescription = 'Get help from our support team';
    
    // If outside working hours, indicate emergency support only
    if (!workingHoursStatus.isOpen && features.tickets?.workingHours?.enabled) {
      ticketDescription = 'Emergency support (user reports) available 24/7';
    }
    
    supportOptions.push({
      label: 'Create Ticket',
      value: 'create_ticket',
      description: ticketDescription,
      emoji: '🎫',
    });
  }

  if (features.suggestions?.enabled !== false) {
    supportOptions.push({
      label: 'Make Suggestion',
      value: 'create_suggestion',
      description: 'Share your ideas with us',
      emoji: '💡',
    });
  }

  if (supportOptions.length === 0) {
    await interaction.reply({
      content:
        '🔧 **Support System Under Maintenance**\nAll support features are currently disabled.',
      flags: MessageFlags.Ephemeral,
    });
    return;
  }

  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId('support_type_select')
    .setPlaceholder('Choose support type...')
    .addOptions(supportOptions);

  const row = new ActionRowBuilder().addComponents(selectMenu);

  // Create support embed with working hours status
  let supportMessage = '🎯 **Support Center**\nWhat can we help you with today?';
  
  // If tickets are not available due to working hours, show status
  if (!workingHoursStatus.isOpen && features.tickets?.workingHours?.enabled) {
    supportMessage += `\n\n${statusDisplay.emoji} **${statusDisplay.title}**\n${statusDisplay.description}`;
    
    const supportEmbed = new EmbedBuilder()
      .setTitle('🎯 Support Center')
      .setDescription('What can we help you with today?')
      .setColor(statusDisplay.color);

    if (statusDisplay.fields) {
      statusDisplay.fields.forEach(field => {
        supportEmbed.addFields(field);
      });
    }

    await interaction.reply({
      embeds: [supportEmbed],
      components: supportOptions.length > 0 ? [row] : [],
      flags: MessageFlags.Ephemeral,
    });
  } else {
    await interaction.reply({
      content: supportMessage,
      components: [row],
      flags: MessageFlags.Ephemeral,
    });
  }
}

export async function handleSupportTypeSelect(interaction, config) {
  try {
    // Schedule ephemeral deletion after 60 seconds
    await scheduleEphemeralDeletion(interaction.id, interaction, 60000);

    const selectedValue = interaction.values[0];

    if (selectedValue === 'create_ticket') {
      const { handleCreateTicketButton } = await import('./tickets.js');
      await handleCreateTicketButton(interaction, config);
    } else if (selectedValue === 'create_suggestion') {
      const { handleCreateSuggestionButton } = await import('./suggestions.js');
      await handleCreateSuggestionButton(interaction, config);
    } else {
      logger.warn('Unknown support type selected', { selectedValue });
      await updateEphemeralStatus(
        interaction, 
        'Invalid support type selected. Please try again.', 
        'error'
      );
    }
  } catch (error) {
    logger.error('Failed to handle support type selection', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace'
    });
    
    try {
      await updateEphemeralStatus(
        interaction, 
        'An error occurred while processing your request. Please try again.', 
        'error'
      );
    } catch (updateError) {
      logger.error('Failed to send error message', {
        error: updateError instanceof Error ? updateError.message : 'Unknown error'
      });
    }
  }
}