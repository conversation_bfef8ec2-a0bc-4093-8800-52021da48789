import {
  EmbedBuilder,
  MessageFlags,
  StringSelectMenuBuilder,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  UserSelectMenuBuilder,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
  AttachmentBuilder
} from 'discord.js';
import Logger from '../../../utils/logger.js';
import { updateEphemeralStatus } from '../utils/ephemeralUtils.js';
import { Buffer } from 'buffer';
import { getTicketsDb } from '../utils/dbUtils.js';

import { showAddNoteModal, handleAddNote, showTicketNotes } from '../utils/notesUtils.js';
import { getTicketSLAStatus } from '../utils/slaUtils.js';
import { createTicketTemplateEmbed, getTicketTemplate } from '../utils/templateUtils.js';
import { isWithinWorkingHours, isCategoryEmergency, formatWorkingHoursStatus } from '../utils/workingHoursUtils.js';

// Create a logger instance
const logger = new Logger({ level: 'info' });

// Store active tickets in memory (in production, use a database)
const activeTickets = new Map();

// New function to load tickets from database on startup
async function loadActiveTickets(guild) {
  try {
    const ticketsCollection = await getTicketsDb();
    const guildTickets = await ticketsCollection.find({ 
      guildId: guild.id, 
      status: { $ne: 'closed' } 
    }).toArray();

    logger.info('Ticket Database Load Attempt', {
      guildId: guild.id,
      guildName: guild.name,
      totalTicketsFound: guildTickets.length,
      ticketDetails: guildTickets.map(ticket => ({
        id: ticket.id,
        channelId: ticket.channelId,
        status: ticket.status
      }))
    });

    for (const ticket of guildTickets) {
      // Verify channel still exists before adding
      const channel = guild.channels.cache.get(ticket.channelId);
      if (!channel) {
        logger.warn('Ticket channel no longer exists', {
          ticketId: ticket.id,
          channelId: ticket.channelId
        });
        continue;
      }

      activeTickets.set(ticket.channelId, {
        id: ticket.id,
        userId: ticket.userId,
        guildId: ticket.guildId,
        channelId: ticket.channelId,
        category: ticket.category,
        categoryName: ticket.categoryName,
        status: ticket.status,
        priority: ticket.priority,
        createdAt: new Date(ticket.createdAt),
        claimedBy: ticket.claimedBy,
      });

      logger.info('Loaded ticket from database', {
        ticketId: ticket.id,
        channelId: ticket.channelId,
        status: ticket.status
      });
    }

    return guildTickets.length;
  } catch (error) {
    logger.error('Failed to load tickets from database', {
      error: error.message,
      guildId: guild.id,
      errorStack: error.stack
    });
    return 0;
  }
}

async function handleCreateTicketButton(interaction, config) {
  try {
    const ticketConfig = config.settings.features.tickets || {};

    // Check if ticket system is enabled
    if (ticketConfig.enabled === false) {
      const maintenanceEmbed = new EmbedBuilder()
        .setTitle('🔧 Tickets Under Maintenance')
        .setDescription(ticketConfig.maintenanceMessage || 'The ticket system is currently unavailable.')
        .setColor(0xff9900);

      await interaction.update({
        embeds: [maintenanceEmbed],
        components: [],
      });
      return;
    }

    // Check working hours status for display
    const workingHoursStatus = isWithinWorkingHours(config);
    const statusDisplay = formatWorkingHoursStatus(workingHoursStatus, config);

    const categories = ticketConfig.categories || [];

    if (categories.length === 0) {
      const noCategories = new EmbedBuilder()
        .setTitle('❌ No Ticket Categories')
        .setDescription('No ticket categories are currently configured.')
        .setColor(0xff0000);

      await interaction.update({
        embeds: [noCategories],
        components: [],
      });
      return;
    }

    // Filter categories based on working hours (reuse existing workingHoursStatus)
    let availableCategories = categories;
    if (!workingHoursStatus.isOpen && ticketConfig.workingHours?.enabled) {
      // Only show emergency categories when support is closed
      const emergencyCategories = ticketConfig.workingHours?.emergencyCategories || ['user_report'];
      availableCategories = categories.filter(cat => emergencyCategories.includes(cat.id));
    }

    // Create select menu for ticket categories
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('ticket_category_select')
      .setPlaceholder(
        !workingHoursStatus.isOpen && ticketConfig.workingHours?.enabled 
          ? 'Choose emergency category' 
          : 'Choose a ticket category'
      )
      .addOptions(
        availableCategories.map((cat) => ({
          label: cat.name,
          value: cat.id,
          description: cat.description || 'No description',
          emoji: cat.emoji || '🎫',
        }))
      );

    const row = new ActionRowBuilder().addComponents(selectMenu);

    let embedTitle = '🎫 Create a New Ticket';
    let embedDescription = 'Please select the category that best describes your issue.';
    
    // Update messaging for emergency-only mode
    if (!workingHoursStatus.isOpen && ticketConfig.workingHours?.enabled) {
      embedTitle = '🚨 Emergency Support Only';
      embedDescription = 'Support is currently offline. Only emergency categories are available.';
    }
    
    const infoEmbed = new EmbedBuilder()
      .setTitle(embedTitle)
      .setDescription(`${embedDescription}\n\n${statusDisplay.emoji} **${statusDisplay.title}**\n${statusDisplay.description}`)
      .setColor(workingHoursStatus.isOpen ? 0x10B981 : 0xFF6B00)
      .setTimestamp();

    // Add working hours info if support is closed
    if (!workingHoursStatus.isOpen && statusDisplay.fields) {
      statusDisplay.fields.forEach(field => {
        infoEmbed.addFields(field);
      });
    }

    await interaction.update({
      embeds: [infoEmbed],
      components: [row],
    });

  } catch (error) {
    logger.error('Error in handleCreateTicketButton', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });

    await updateEphemeralStatus(
      interaction,
      '❌ Failed to open ticket creation menu. Please try again later.',
      'error'
    );
  }
}

async function handleTicketCategorySelect(interaction, config) {
  try {
    if (!interaction.guild) {
      logger.error('Interaction not in a guild context');
      await interaction.reply({
        content: '❌ This command cannot be used in direct messages.',
        flags: MessageFlags.Ephemeral,
      });
      return;
    }

    const categoryValue = interaction.values[0];
    const ticketConfig = config.settings.features?.tickets || {};

    // Check working hours before creating ticket
    const workingHoursStatus = isWithinWorkingHours(config);
    const isEmergencyCategory = isCategoryEmergency(categoryValue, config);

    // If outside working hours and not an emergency category, block ticket creation
    if (!workingHoursStatus.isOpen && !isEmergencyCategory) {
      const statusDisplay = formatWorkingHoursStatus(workingHoursStatus, config);
      
      const closedEmbed = new EmbedBuilder()
        .setTitle(`${statusDisplay.emoji} ${statusDisplay.title}`)
        .setDescription(statusDisplay.description)
        .setColor(statusDisplay.color);

      if (statusDisplay.fields) {
        statusDisplay.fields.forEach(field => {
          closedEmbed.addFields(field);
        });
      }

      closedEmbed.addFields({
        name: '🚨 Emergency Support',
        value: 'For urgent matters (user reports), you can still create a ticket as emergency support is available 24/7.',
        inline: false
      });

      await interaction.reply({
        embeds: [closedEmbed],
        flags: MessageFlags.Ephemeral,
      });

      logger.info('Ticket creation blocked - outside working hours', {
        userId: interaction.user.id,
        category: categoryValue,
        workingHoursStatus: workingHoursStatus.reason,
        isEmergency: isEmergencyCategory
      });

      return;
    }

    if (!ticketConfig || !ticketConfig.categories || !ticketConfig.categories.length) {
      logger.error('Ticket system not properly configured');
      const errorEmbed = new EmbedBuilder()
        .setTitle('❌ Ticket System Not Configured')
        .setDescription('The ticket system has not been properly configured.')
        .setColor(0xff0000);

      await interaction.reply({
        embeds: [errorEmbed],
        flags: MessageFlags.Ephemeral,
      });
      return;
    }

    const category = ticketConfig.categories.find(
      (cat) => cat.id === categoryValue
    );

    if (!category) {
      logger.error('Invalid ticket category selected');
      const errorEmbed = new EmbedBuilder()
        .setTitle('❌ Invalid Category')
        .setDescription('The selected ticket category is no longer available.')
        .setColor(0xff0000);

      await interaction.reply({
        embeds: [errorEmbed],
        flags: MessageFlags.Ephemeral,
      });
      return;
    }

    const ticketId = Math.random().toString(36).substring(2, 11).toUpperCase();
    const ticketChannelName = `ticket-${category.id}-${ticketId}`;
    const ticketCategory = interaction.guild.channels.cache.get(ticketConfig.categoryId);

    if (!ticketCategory || ticketCategory.type !== 4) { // 4 is ChannelType.GuildCategory
      logger.error('Invalid or missing ticket category in config', {
        categoryId: ticketConfig.categoryId,
        guildId: interaction.guildId,
        ticketCategoryExists: !!ticketCategory,
        ticketCategoryType: ticketCategory?.type,
      });
      await updateEphemeralStatus(
        interaction,
        '❌ Failed to create ticket: Misconfigured ticket category.',
        'error'
      );
      return;
    }

    // Filter and validate staff roles
    const validStaffRoles = (ticketConfig.staffRoles || [])
      .map(roleId => {
        try {
          const role = interaction.guild.roles.cache.get(roleId);
          return role ? {
            id: role.id,
            allow: ['ViewChannel', 'SendMessages', 'ReadMessageHistory']
          } : null;
        } catch (error) {
          logger.warn('Invalid staff role', {
            roleId,
            error: error.message
          });
          return null;
        }
      })
      .filter(role => role !== null);

    // Debug logging for staff roles
    logger.info('Ticket Staff Roles', {
      configStaffRoles: ticketConfig.staffRoles,
      validStaffRoleIds: validStaffRoles.map(role => role.id),
      userRoles: interaction.member.roles.cache.map(role => role.id)
    });

    // Create the ticket channel
    let ticketChannel;
    try {
      ticketChannel = await interaction.guild.channels.create({
        name: ticketChannelName,
        type: 0, // ChannelType.GuildText
        parent: ticketCategory.id,
        permissionOverwrites: [
          {
            id: interaction.guild.id,
            deny: ['ViewChannel'],
          },
          {
            id: interaction.user.id,
            allow: ['ViewChannel', 'SendMessages', 'ReadMessageHistory'],
          },
          {
            id: interaction.client.user.id, // Bot's permissions
            allow: ['ViewChannel', 'SendMessages', 'ReadMessageHistory', 'ManageChannels'],
          },
          // Add valid staff roles to view channel
          ...validStaffRoles,
        ],
      });
      logger.info('Ticket channel created successfully', { channelId: ticketChannel.id, ticketId });
    } catch (channelError) {
      logger.error('Failed to create ticket channel', {
        error: channelError.message,
        guildId: interaction.guildId,
        stack: channelError.stack,
      });
      await updateEphemeralStatus(
        interaction,
        '❌ Failed to create ticket channel. Please check bot permissions.',
        'error'
      );
      return;
    }

    // Create the enhanced ticket embed like in the image
    const newTicketEmbed = new EmbedBuilder()
      .setTitle(`🔒 ${category.name}`)
      .setDescription(`**#${ticketId}**`)
      .setColor(0x2B2D31)
      .addFields(
        {
          name: `${category.name}`,
          value: `Professional and discreet assistance for sensitive inquiries.`,
          inline: false
        },
        {
          name: `🔒 ${category.name} Guidelines`,
          value: `Initial Request: ${category.description || 'No description available'}`,
          inline: false
        },
        {
          name: 'Communication Protocol:',
          value: '• Maintain respectful communication\n• Provide clear, concise details\n• Expect professional handling\n• All information is strictly confidential',
          inline: false
        },
        {
          name: '👤 Creator',
          value: `<@${interaction.user.id}>`,
          inline: true
        },
        {
          name: '📂 Category',
          value: category.name,
          inline: true
        },
        {
          name: '⚡ Priority',
          value: '🟡 normal',
          inline: true
        },
        {
          name: '📊 Status',
          value: '🟢 open',
          inline: true
        },
        {
          name: '📅 Created',
          value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
          inline: true
        },
        {
          name: '👥 Claimed By',
          value: 'None',
          inline: true
        }
      )
      .setTimestamp()
      .setFooter({ text: `${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}` });

    const actionRows = await createTicketActionRow({
      id: ticketId,
      userId: interaction.user.id,
      claimedBy: null,
      status: 'open',
      priority: 'normal',
      createdAt: new Date(),
    }, {
      user: interaction.user,
      member: interaction.member,
      guild: interaction.guild,
      channel: interaction.channel
    }, ticketConfig.staffRoles || []);

    await ticketChannel.send({
      embeds: [newTicketEmbed],
      components: actionRows
    });

    // Get template to determine priority first
    const template = getTicketTemplate(category.id, ticketConfig);
    
    // Send template-specific instructions and components
    const templateData = createTicketTemplateEmbed(category.id, ticketConfig, {
      id: ticketId,
      categoryName: category.name,
      priority: template?.priority || 'normal',
      createdAt: new Date()
    });

    await ticketChannel.send({
      embeds: [templateData.embed],
      components: templateData.components
    });

    logger.info('Ticket created with components and template', {
      ticketId,
      category: category.id,
      hasTemplate: !!templateData,
      hasComponents: templateData.components.length > 0,
      components: actionRows.map(row => row.components.map(comp => comp.customId))
    });

    // Store ticket data
    const ticketDataToStore = {
      id: ticketId,
      userId: interaction.user.id,
      guildId: interaction.guildId,
      channelId: ticketChannel.id,
      category: category.id,
      categoryName: category.name,
      status: 'open',
      priority: template?.priority || 'normal',
      createdAt: new Date(),
    };
    
    try {
      activeTickets.set(ticketChannel.id, ticketDataToStore);
      logger.info('Ticket stored in memory', {
        ticketId,
        channelId: ticketChannel.id,
        activeTicketsCount: activeTickets.size,
        storedData: ticketDataToStore
      });
    } catch (cacheError) {
      logger.error('Failed to store ticket in memory', {
        error: cacheError.message,
        ticketId,
        channelId: ticketChannel.id,
        stack: cacheError.stack,
      });
    }

    // Store ticket in database after creating
    try {
      const ticketsCollection = await getTicketsDb();
      const dbTicketData = {
        id: ticketId,
        userId: interaction.user.id,
        guildId: interaction.guildId,
        channelId: ticketChannel.id,
        category: category.id,
        categoryName: category.name,
        status: 'open',
        priority: template?.priority || 'normal',
        createdAt: new Date(),
      };
      
      const insertResult = await ticketsCollection.insertOne(dbTicketData);
      
      logger.info('Ticket stored in database', {
        ticketId,
        channelId: ticketChannel.id,
        insertedId: insertResult.insertedId,
        acknowledged: insertResult.acknowledged
      });
    } catch (dbError) {
      logger.error('Failed to store ticket in database', {
        error: dbError.message,
        ticketId,
        channelId: ticketChannel.id,
        stack: dbError.stack,
      });
    }

    await interaction.update({
      content: `✅ Your ticket has been created: ${ticketChannel}`,
      components: [],
      flags: MessageFlags.Ephemeral,
    });

  } catch (error) {
    logger.error('Error in handleTicketCategorySelect', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId,
      categoryValue: interaction.values[0],
      stack: error instanceof Error ? error.stack : 'No stack trace',
    });

    await updateEphemeralStatus(
      interaction,
      '❌ Failed to process ticket category. Please try again later.',
      'error'
    );
  }
}

async function handleTicketButtonInteractions(interaction, config) {
  try {
    const { customId } = interaction;

    if (customId.startsWith('ticket_claim')) {
      await handleClaimTicket(interaction, config);
    } else if (customId === 'ticket_set_priority') {
      await handleSetPriority(interaction, config);
    } else if (customId === 'ticket_transfer') {
      await handleTransferTicket(interaction, config);
    } else if (customId === 'ticket_close') {
      await handleCloseTicket(interaction, config);
    } else if (customId.startsWith('ticket_confirm_close_')) {
      await handleConfirmCloseTicket(interaction, config);
      } else if (customId.startsWith('ticket_cancel_close_')) {
    await handleCancelCloseTicket(interaction, config);
  } else if (customId === 'ticket_add_note') {
    await handleAddNoteButton(interaction, config);
  } else if (customId === 'ticket_view_notes') {
    await handleViewNotesButton(interaction, config);
  } else {
    logger.warn('Unknown ticket button interaction', { customId: interaction.customId });
    await updateEphemeralStatus(interaction, `🎫 Unknown ticket button: ${interaction.customId}`, 'warn');
  }
  } catch (error) {
    logger.error('Error in handleTicketButtonInteractions', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });
  }
}

async function handleTicketSelectMenuInteractions(interaction, config) {
  try {
    const { customId } = interaction;

    if (customId.startsWith('ticket_priority_select_')) {
      await handlePrioritySelect(interaction, config);
    } else if (customId.startsWith('ticket_transfer_select')) {
      await handleTransferSelect(interaction, config);
    } else if (customId === 'ticket_category_select') {
      await handleTicketCategorySelect(interaction, config);
    } else {
      logger.warn('Unknown ticket select menu interaction', { customId: interaction.customId });
      await updateEphemeralStatus(interaction, `🎫 Unknown ticket select menu: ${interaction.customId}`, 'warn');
    }
  } catch (error) {
    logger.error('Error in handleTicketSelectMenuInteractions', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });
  }
}

async function handlePrioritySelect(interaction, config) {
  try {
    const ticketId = interaction.customId.replace('ticket_priority_select_', '');
    const newPriority = interaction.values[0];
    const ticketData = activeTickets.get(interaction.channelId);

    if (!ticketData || ticketData.id !== ticketId) {
      await updateEphemeralStatus(interaction, '❌ Ticket data not found or mismatch.', 'error');
      return;
    }

    // Update ticket priority
    ticketData.priority = newPriority;
    activeTickets.set(interaction.channelId, ticketData);

    // Update the ticket embed
    await updateTicketEmbed(interaction.channel, ticketData, config);

    await interaction.update({
      content: `🚨 Ticket priority updated to **${newPriority.charAt(0).toUpperCase() + newPriority.slice(1)}**`,
      components: [],
    });

    logger.info('Ticket priority updated', {
      ticketId: ticketData.id,
      newPriority,
      userId: interaction.user.id,
    });
  } catch (error) {
    logger.error('Error in handlePrioritySelect', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });
    await updateEphemeralStatus(interaction, '❌ Failed to update ticket priority. Please try again later.', 'error');
  }
}

async function handleSetPriority(interaction, config) {
  try {
    const ticketData = await getTicketData(interaction.channelId);
    
    if (!ticketData) {
      await updateEphemeralStatus(interaction, '❌ Ticket data not found.', 'error');
      return;
    }

    // Comprehensive staff role check
    const configStaffRoles = 
      config?.settings?.features?.tickets?.staffRoles || 
      config?.settings?.features?.staffRoles || 
      config?.settings?.staffRoles || 
      [];

    logger.info('Set Priority Staff Role Check', {
      configStaffRoles,
      userRoles: interaction.member.roles.cache.map(role => role.id),
      hasStaffRole: configStaffRoles.some(roleId => 
        interaction.member.roles.cache.has(roleId)
      )
    });

    const hasStaffRole = configStaffRoles.some(roleId => 
      interaction.member.roles.cache.has(roleId)
    );

    if (!hasStaffRole) {
      await updateEphemeralStatus(
        interaction, 
        '❌ Only staff members can set ticket priority.', 
        'error'
      );
      return;
    }

    const prioritySelect = new StringSelectMenuBuilder()
      .setCustomId(`ticket_priority_select_${ticketData.id}`)
      .setPlaceholder('Select Ticket Priority');

    const priorities = config.settings.features.tickets.priorities || {
      low: { name: 'Low', emoji: '🟢', description: 'Low priority issue' },
      normal: { name: 'Normal', emoji: '🟡', description: 'Normal priority issue' },
      high: { name: 'High', emoji: '🟠', description: 'High priority issue' },
      critical: { name: 'Critical', emoji: '🔴', description: 'Critical priority issue' }
    };

    prioritySelect.addOptions(
      Object.entries(priorities).map(([id, data]) => ({
        label: data.name || id.charAt(0).toUpperCase() + id.slice(1),
        value: id,
        description: data.description || `Set priority to ${id}`,
        emoji: data.emoji || '💥',
      }))
    );

    const row = new ActionRowBuilder().addComponents(prioritySelect);

    await interaction.reply({
      content: '🚨 **Change Ticket Priority**\nSelect the new priority level:',
      components: [row],
      flags: MessageFlags.Ephemeral
    });
  } catch (error) {
    logger.error('Error in handleSetPriority', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId,
      stack: error instanceof Error ? error.stack : 'No stack trace'
    });
    await updateEphemeralStatus(interaction, '❌ Failed to open priority selection. Please try again later.', 'error');
  }
}

async function handleCloseTicket(interaction, config) {
  try {
    const ticketData = await getTicketData(interaction.channelId);
    
    if (!ticketData) {
      await updateEphemeralStatus(interaction, '❌ Ticket data not found.', 'error');
      return;
    }

    const confirmRow = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId(`ticket_confirm_close_${ticketData.id}`)
        .setLabel('Confirm Close')
        .setStyle(ButtonStyle.Danger)
        .setEmoji('🔒'),
      new ButtonBuilder()
        .setCustomId(`ticket_cancel_close_${ticketData.id}`)
        .setLabel('Cancel')
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('❌')
    );

    await interaction.reply({
      content: '⚠️ **Close Ticket Confirmation**\nAre you sure you want to close this ticket? This action cannot be undone.',
      components: [confirmRow],
      flags: MessageFlags.Ephemeral
    });

  } catch (error) {
    logger.error('Error in handleCloseTicket', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });
    await updateEphemeralStatus(interaction, '❌ Failed to initiate ticket closure. Please try again later.', 'error');
  }
}

async function handleConfirmCloseTicket(interaction, config) {
  try {
    const ticketId = interaction.customId.replace('ticket_confirm_close_', '');
    const ticketData = activeTickets.get(interaction.channelId);

    if (!ticketData || ticketData.id !== ticketId) {
      await updateEphemeralStatus(interaction, '❌ Ticket data not found or mismatch.', 'error');
      return;
    }

    // In a real application, you'd persist this closure to a database
    ticketData.status = 'closed';
    ticketData.closedBy = interaction.user.id;
    ticketData.closedAt = new Date();
    activeTickets.delete(interaction.channelId); // Remove from active tickets

    // Update the ticket embed to reflect closed status and remove buttons
    await updateTicketEmbed(interaction.channel, { ...ticketData, status: 'closed' }, config);

    // Update ticket status in database
    const ticketsCollection = await getTicketsDb();
    await ticketsCollection.updateOne(
      { channelId: interaction.channelId },
      { $set: {
        status: 'closed',
        closedBy: interaction.user.id,
        closedAt: new Date()
      }}
    );

    await interaction.update({
      content: `🔒 Ticket #${ticketData.id} has been closed by ${interaction.user}.`,
      components: [],
    });

    // Generate and send transcript to log channel before closing
    if (config.settings.features.tickets.logChannel) {
      const logChannel = interaction.guild.channels.cache.get(config.settings.features.tickets.logChannel);
      if (logChannel) {
        try {
          // Generate transcript for logging
          const messages = await interaction.channel.messages.fetch({ limit: 100 });
          const { generateHTMLTranscript, createTranscriptAttachment } = await import('../utils/transcriptUtils.js');
          const html = generateHTMLTranscript(messages, interaction.channel, ticketData, interaction.guild);
          const attachment = createTranscriptAttachment(html, ticketData.id);

          const closeLogEmbed = new EmbedBuilder()
            .setTitle('🔒 Ticket Closed')
            .setDescription(`Ticket #${ticketData.id} in ${interaction.channel} was closed by ${interaction.user}.`)
            .addFields(
              { name: 'Category', value: ticketData.categoryName, inline: true },
              { name: 'Created By', value: `<@${ticketData.userId}>`, inline: true },
              { name: 'Closed By', value: `<@${interaction.user.id}>`, inline: true },
              { name: 'Messages', value: messages.size.toString(), inline: true },
              { name: 'Duration', value: `${Math.floor((Date.now() - ticketData.createdAt.getTime()) / (1000 * 60 * 60))} hours`, inline: true }
            )
            .setColor(0xFF0000)
            .setTimestamp();

          await logChannel.send({ 
            embeds: [closeLogEmbed],
            files: [attachment]
          });

          logger.info('Ticket transcript sent to log channel', {
            ticketId: ticketData.id,
            logChannelId: logChannel.id
          });
        } catch (error) {
          logger.error('Failed to generate transcript for log channel', {
            error: error.message,
            ticketId: ticketData.id
          });

          // Fallback to basic log without transcript
          const closeLogEmbed = new EmbedBuilder()
            .setTitle('🔒 Ticket Closed')
            .setDescription(`Ticket #${ticketData.id} in ${interaction.channel} was closed by ${interaction.user}.`)
            .addFields(
              { name: 'Category', value: ticketData.categoryName, inline: true },
              { name: 'Created By', value: `<@${ticketData.userId}>`, inline: true },
              { name: 'Closed By', value: `<@${interaction.user.id}>`, inline: true }
            )
            .setColor(0xFF0000)
            .setTimestamp();
          await logChannel.send({ embeds: [closeLogEmbed] });
        }
      }
    }

    // Delete the ticket channel after a delay (e.g., 5 seconds)
    setTimeout(async () => {
      try {
        await interaction.channel.delete('Ticket closed and channel cleanup.');
        logger.info('Ticket channel deleted', { ticketId: ticketData.id, channelId: interaction.channelId });
      } catch (err) {
        logger.error('Failed to delete ticket channel', { ticketId: ticketData.id, channelId: interaction.channelId, error: err.message });
      }
    }, 5000); // 5 seconds

    logger.info('Ticket closed', {
      ticketId: ticketData.id,
      userId: interaction.user.id,
    });

  } catch (error) {
    logger.error('Error in handleConfirmCloseTicket', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });
    await updateEphemeralStatus(interaction, '❌ Failed to close ticket. Please try again later.', 'error');
  }
}

async function handleCancelCloseTicket(interaction, config) {
  try {
    await interaction.update({
      content: '✅ Ticket closure cancelled.',
      components: [],
    });

    logger.info('Ticket closure cancelled', {
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });
  } catch (error) {
    logger.error('Error in handleCancelCloseTicket', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });
  }
}



async function handleAddNoteButton(interaction, config) {
  try {
    const ticketData = await getTicketData(interaction.channelId);

    if (!ticketData) {
      await updateEphemeralStatus(
        interaction,
        '❌ Ticket data not found.',
        'error'
      );
      return;
    }

    // Check if user has staff permissions
    const configStaffRoles = 
      config?.settings?.features?.tickets?.staffRoles || 
      config?.settings?.staffRoles || 
      [];

    const hasStaffRole = configStaffRoles.some(roleId => 
      interaction.member.roles.cache.has(roleId)
    );

    if (!hasStaffRole) {
      await updateEphemeralStatus(
        interaction,
        '❌ Only staff members can add internal notes.',
        'error'
      );
      return;
    }

    await showAddNoteModal(interaction, ticketData.id);

  } catch (error) {
    logger.error('Error in handleAddNoteButton', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId
    });

    await updateEphemeralStatus(
      interaction,
      '❌ Failed to open note modal. Please try again later.',
      'error'
    );
  }
}

async function handleViewNotesButton(interaction, config) {
  try {
    const ticketData = await getTicketData(interaction.channelId);

    if (!ticketData) {
      await updateEphemeralStatus(
        interaction,
        '❌ Ticket data not found.',
        'error'
      );
      return;
    }

    // Check if user has staff permissions
    const configStaffRoles = 
      config?.settings?.features?.tickets?.staffRoles || 
      config?.settings?.staffRoles || 
      [];

    const hasStaffRole = configStaffRoles.some(roleId => 
      interaction.member.roles.cache.has(roleId)
    );

    if (!hasStaffRole) {
      await updateEphemeralStatus(
        interaction,
        '❌ Only staff members can view internal notes.',
        'error'
      );
      return;
    }

    await showTicketNotes(interaction, ticketData.id);

  } catch (error) {
    logger.error('Error in handleViewNotesButton', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId
    });

    await updateEphemeralStatus(
      interaction,
      '❌ Failed to load notes. Please try again later.',
      'error'
    );
  }
}

async function handleTransferTicket(interaction, config) {
  try {
    const ticketData = await getTicketData(interaction.channelId);

    if (!ticketData) {
      await updateEphemeralStatus(
        interaction,
        '❌ Ticket data not found.',
        'error'
      );
      return;
    }

    // Check if the current user is the one who claimed the ticket
    if (ticketData.claimedBy !== interaction.user.id) {
      await updateEphemeralStatus(
        interaction,
        '❌ Only the ticket claimer can transfer the ticket.',
        'error'
      );
      return;
    }

    // Get staff roles from configuration
    const staffRoles = 
      config?.settings?.features?.tickets?.staffRoles || 
      config?.settings?.staffRoles || 
      [];

    // Filter staff members
    const staffMembers = interaction.guild.members.cache
      .filter(member => 
        // Check if member has any of the staff roles
        staffRoles.some(roleId => member.roles.cache.has(roleId)) &&
        // Exclude the current user
        member.id !== interaction.user.id
      );

    // If no other staff members, inform the user
    if (staffMembers.size === 0) {
      await updateEphemeralStatus(
        interaction,
        '❌ No other staff members available to transfer the ticket.',
        'error'
      );
      return;
    }

    // Create user select menu with only staff members
    const userSelect = new UserSelectMenuBuilder()
      .setCustomId('ticket_transfer_select')
      .setPlaceholder('Select a staff member to transfer the ticket to')
      .setMaxValues(1);

    const actionRow = new ActionRowBuilder().addComponents(userSelect);

    // Log the staff members for debugging
    logger.info('Staff Members for Transfer', {
      staffRoles,
      staffMemberIds: Array.from(staffMembers.keys()),
      totalStaffMembers: staffMembers.size
    });

    // Respond with ephemeral user select menu
    await interaction.reply({
      content: 'Select a staff member to transfer the ticket to:',
      components: [actionRow],
      flags: MessageFlags.Ephemeral
    });

  } catch (error) {
    logger.error('Error in handleTransferTicket', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId,
      stack: error instanceof Error ? error.stack : 'No stack trace'
    });

    await updateEphemeralStatus(
      interaction,
      '❌ Failed to initiate ticket transfer. Please try again later.',
      'error'
    );
  }
}

async function handleTransferSelect(interaction, config) {
  try {
    const selectedUserId = interaction.values[0];
    const ticketData = activeTickets.get(interaction.channelId);

    if (!ticketData) {
      await updateEphemeralStatus(
        interaction,
        '❌ Ticket data not found.',
        'error'
      );
      return;
    }

    // Verify the selected user is a staff member
    const staffRoles = config.settings.features.tickets.staffRoles || [];
    const selectedMember = await interaction.guild.members.fetch(selectedUserId);

    const isStaffMember = selectedMember.roles.cache.some(role =>
      staffRoles.includes(role.id)
    );

    if (!isStaffMember) {
      await updateEphemeralStatus(
        interaction,
        '❌ You can only transfer to a staff member.',
        'error'
      );
      return;
    }

    // Update ticket data
    ticketData.claimedBy = selectedUserId;
    activeTickets.set(interaction.channelId, ticketData);

    // Update ticket embed
    await updateTicketEmbed(interaction.channel, ticketData, config);

    // Notify in ticket channel
    await interaction.channel.send({
      content: `🔄 Ticket transferred from ${interaction.user} to ${selectedMember}`
    });

    // Respond with a temporary ephemeral message
    await interaction.reply({
      content: `✅ Ticket transferred to ${selectedMember}`,
      flags: MessageFlags.Ephemeral
    });

    logger.info('Ticket transferred', {
      ticketId: ticketData.id,
      from: interaction.user.id,
      to: selectedUserId,
    });

  } catch (error) {
    logger.error('Error in handleTransferSelect', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });

    await updateEphemeralStatus(
      interaction,
      '❌ Failed to transfer ticket. Please try again later.',
      'error'
    );
  }
}

// Modify updateTicketEmbed to match the image style
async function updateTicketEmbed(channel, ticketData, config) {
  try {
    // Fetch the original ticket message
    const messages = await channel.messages.fetch({ limit: 50 });
    const ticketMessage = messages.find(msg =>
      msg.embeds.length > 0 &&
      (msg.embeds[0].title?.includes(`#${ticketData.id}`) || msg.embeds[0].description?.includes(`#${ticketData.id}`))
    );

    if (!ticketMessage) return;

    const guild = channel.guild;
    const user = await guild.members.fetch(ticketData.userId);

    let claimedByText = 'None';
    if (ticketData.claimedBy) {
      const claimedMember = await guild.members.fetch(ticketData.claimedBy);
      claimedByText = claimedMember.toString();
    }

    const statusEmojis = {
      open: '🟢',
      in_progress: '🟡',
      pending: '🟠',
      resolved: '✅',
      closed: '🔒'
    };

    const priorityEmojis = {
      low: '🟢',
      normal: '🟡',
      high: '🟠',
      critical: '🔴'
    };

    // Get SLA status
    const slaStatus = getTicketSLAStatus(ticketData);

    const updatedEmbed = new EmbedBuilder()
      .setTitle(`🔒 ${ticketData.categoryName}`)
      .setDescription(`**#${ticketData.id}**`)
      .setColor(slaStatus.color)
      .addFields(
        {
          name: `${ticketData.categoryName}`,
          value: `Professional and discreet assistance for sensitive inquiries.`,
          inline: false
        },
        {
          name: `🔒 ${ticketData.categoryName} Guidelines`,
          value: `Initial Request: ${ticketData.categoryName} content support`,
          inline: false
        },
        {
          name: 'Communication Protocol:',
          value: '• Maintain respectful communication\n• Provide clear, concise details\n• Expect professional handling\n• All information is strictly confidential',
          inline: false
        },
        {
          name: '👤 Creator',
          value: user.toString(),
          inline: true
        },
        {
          name: '📂 Category',
          value: ticketData.categoryName,
          inline: true
        },
        {
          name: '⚡ Priority',
          value: `${priorityEmojis[ticketData.priority] || '🟡'} ${ticketData.priority}`,
          inline: true
        },
        {
          name: '📊 Status',
          value: `${statusEmojis[ticketData.status] || '🟢'} ${ticketData.status}`,
          inline: true
        },
        {
          name: '📅 Created',
          value: `<t:${Math.floor(ticketData.createdAt.getTime() / 1000)}:R>`,
          inline: true
        },
        {
          name: '👥 Claimed By',
          value: claimedByText,
          inline: true
        },
        {
          name: '⏱️ SLA Status',
          value: `${slaStatus.emoji} ${slaStatus.status} (${slaStatus.isOverdue ? 'Overdue by' : 'Time left'}: ${slaStatus.formattedTime})`,
          inline: true
        }
      )
      .setTimestamp(ticketData.createdAt)
      .setFooter({ text: `${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}` });

    // Create action rows with claim/transfer buttons
    const staffRoles = config?.settings?.features?.tickets?.staffRoles || [];
    const actionRows = await createTicketActionRow(ticketData, {
      user: { id: ticketData.userId },
      channel,
      guild,
      member: await guild.members.fetch(ticketData.userId),
      config
    }, staffRoles);

    // Edit the message with updated embed and action rows
    await ticketMessage.edit({
      embeds: [updatedEmbed],
      components: actionRows
    });
  } catch (error) {
    logger.error('Error updating ticket embed', {
      error: error.message,
      ticketId: ticketData.id,
      stack: error.stack,
    });
  }
}

async function handleClaimTicket(interaction, config) {
  try {
    const ticketData = await getTicketData(interaction.channelId);

    if (!ticketData) {
      await updateEphemeralStatus(
        interaction,
        '❌ Ticket data not found.',
        'error'
      );
      return;
    }

    // Check if user has staff role
    const configStaffRoles = 
      config?.settings?.features?.tickets?.staffRoles || 
      config?.settings?.staffRoles || 
      [];

    const hasStaffRole = configStaffRoles.some(roleId => 
      interaction.member.roles.cache.has(roleId)
    );

    if (!hasStaffRole) {
      await updateEphemeralStatus(
        interaction,
        '❌ Only staff members can claim tickets.',
        'error'
      );
      return;
    }

    // If already claimed by someone else
    if (ticketData.claimedBy && ticketData.claimedBy !== interaction.user.id) {
      const claimedByMember = await interaction.guild.members.fetch(ticketData.claimedBy);
      await updateEphemeralStatus(
        interaction,
        `❌ This ticket is already claimed by ${claimedByMember}.`,
        'error'
      );
      return;
    }

    // Toggle claim/unclaim
    if (ticketData.claimedBy === interaction.user.id) {
      // Unclaim
      delete ticketData.claimedBy;
      ticketData.status = 'open';
    } else {
      // Claim
      ticketData.claimedBy = interaction.user.id;
      ticketData.status = 'in_progress';
    }

    // Update ticket in active tickets and database
    activeTickets.set(interaction.channelId, ticketData);
    
    // Update ticket in database
    const ticketsCollection = await getTicketsDb();
    await ticketsCollection.updateOne(
      { channelId: interaction.channelId },
      { $set: {
        claimedBy: ticketData.claimedBy,
        status: ticketData.status
      }}
    );

    // Update ticket embed
    await updateTicketEmbed(interaction.channel, ticketData, config);

    // Respond with a temporary ephemeral message
    await interaction.reply({
      content: ticketData.claimedBy
        ? `✅ Ticket claimed by ${interaction.user}`
        : '✅ Ticket unclaimed',
      flags: MessageFlags.Ephemeral
    });

    logger.info('Ticket claim status changed', {
      ticketId: ticketData.id,
      claimedBy: ticketData.claimedBy || 'None',
      userId: interaction.user.id,
    });

  } catch (error) {
    logger.error('Error in handleClaimTicket', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId,
      stack: error instanceof Error ? error.stack : 'No stack trace'
    });

    await updateEphemeralStatus(
      interaction,
      '❌ Failed to process ticket claim. Please try again later.',
      'error'
    );
  }
}

async function createTicketActionRow(ticketData, interaction, config, staffRoles = []) {
  const actionRows = [];

  // Claim button logic
  const claimButton = new ButtonBuilder()
    .setCustomId('ticket_claim')
    .setLabel(
      ticketData.claimedBy 
        ? (ticketData.claimedBy === interaction.user.id ? 'Unclaim' : 'Already Claimed') 
        : 'Claim Ticket'
    )
    .setStyle(
      ticketData.claimedBy 
        ? (ticketData.claimedBy === interaction.user.id ? ButtonStyle.Secondary : ButtonStyle.Danger) 
        : ButtonStyle.Success
    )
    .setEmoji('✋');

  // Check if user has staff role
  const configStaffRoles = 
    config?.settings?.features?.tickets?.staffRoles || 
    config?.settings?.staffRoles || 
    staffRoles || 
    [];

  const hasStaffRole = configStaffRoles.some(roleId => 
    interaction.member.roles.cache.has(roleId)
  );

  // Components array to dynamically add buttons
  const components = [claimButton];

  // Transfer button - only when ticket is claimed
  if (ticketData.claimedBy) {
    const transferButton = new ButtonBuilder()
      .setCustomId('ticket_transfer')
      .setLabel('Transfer')
      .setStyle(ButtonStyle.Secondary)
      .setEmoji('🔄');
    
    components.push(transferButton);
  }

  // Priority button
  const setPriorityButton = new ButtonBuilder()
    .setCustomId('ticket_set_priority')
    .setLabel(hasStaffRole ? 'Set Priority' : 'Set Priority')
    .setStyle(ButtonStyle.Secondary)
    .setEmoji('⚡');

  components.push(setPriorityButton);

  // Staff-only buttons
  if (hasStaffRole) {
    // Add note button
    const addNoteButton = new ButtonBuilder()
      .setCustomId('ticket_add_note')
      .setLabel('Add Note')
      .setStyle(ButtonStyle.Secondary)
      .setEmoji('📝');

    // View notes button
    const viewNotesButton = new ButtonBuilder()
      .setCustomId('ticket_view_notes')
      .setLabel('View Notes')
      .setStyle(ButtonStyle.Secondary)
      .setEmoji('👁️');

    // Check if we need a second row for staff buttons
    if (components.length + 2 > 5) {
      // Create first row with existing components
      const row1 = new ActionRowBuilder().addComponents(components);
      actionRows.push(row1);

      // Create second row for staff buttons and close
      const closeTicketButton = new ButtonBuilder()
        .setCustomId('ticket_close')
        .setLabel('Close Ticket')
        .setStyle(ButtonStyle.Danger)
        .setEmoji('🔒')
        .setDisabled(ticketData.status === 'closed');

      const row2 = new ActionRowBuilder().addComponents([addNoteButton, viewNotesButton, closeTicketButton]);
      actionRows.push(row2);
    } else {
      // Add staff buttons to existing row
      components.push(addNoteButton, viewNotesButton);
      
      // Close ticket button
      const closeTicketButton = new ButtonBuilder()
        .setCustomId('ticket_close')
        .setLabel('Close Ticket')
        .setStyle(ButtonStyle.Danger)
        .setEmoji('🔒')
        .setDisabled(ticketData.status === 'closed');

      components.push(closeTicketButton);

      const row = new ActionRowBuilder().addComponents(components);
      actionRows.push(row);
    }
  } else {
    // Non-staff users only get close button
    const closeTicketButton = new ButtonBuilder()
      .setCustomId('ticket_close')
      .setLabel('Close Ticket')
      .setStyle(ButtonStyle.Danger)
      .setEmoji('🔒')
      .setDisabled(ticketData.status === 'closed');

    components.push(closeTicketButton);

    const row = new ActionRowBuilder().addComponents(components);
    actionRows.push(row);
  }

  return actionRows;
}

// Utility functions for ticket management

// Enhanced getTicketData function with more logging
async function getTicketData(channelId) {
  logger.info('Ticket Data Lookup', {
    channelId,
    activeTicketsCount: activeTickets.size,
    activeTicketIds: Array.from(activeTickets.keys()),
    isTicketInCache: activeTickets.has(channelId),
  });
  
  const ticketData = activeTickets.get(channelId);
  
  if (!ticketData) {
    // If not in memory, try to fetch from database
    logger.warn('Ticket not found in cache, attempting database lookup', { channelId });
    try {
      const ticketsCollection = await getTicketsDb();
      const ticket = await ticketsCollection.findOne({ channelId });
      
      logger.warn('Ticket Data Retrieval Attempt', {
        channelId,
        databaseLookup: !!ticket,
        ticketFound: ticket ? {
          id: ticket.id,
          status: ticket.status,
          guildId: ticket.guildId,
          claimedBy: ticket.claimedBy,
        } : null
      });

      if (ticket) {
        // Reconstruct and cache ticket data
        const reconstructedTicket = {
          id: ticket.id,
          userId: ticket.userId,
          guildId: ticket.guildId,
          channelId: ticket.channelId,
          category: ticket.category,
          categoryName: ticket.categoryName,
          status: ticket.status,
          priority: ticket.priority,
          createdAt: new Date(ticket.createdAt),
          claimedBy: ticket.claimedBy,
        };
        
        activeTickets.set(channelId, reconstructedTicket);
        return reconstructedTicket;
      }
    } catch (error) {
      logger.error('Error retrieving ticket from database', {
        channelId,
        error: error.message,
        errorStack: error.stack
      });
    }
  }
  
  return ticketData;
}

// Get all active tickets for a specific guild
async function getAllActiveTickets(guildId) {
  return Array.from(activeTickets.values()).filter(ticket => ticket.guildId === guildId);
}

// Get active ticket for a specific user in a guild
async function getUserActiveTicket(userId, guildId) {
  return Array.from(activeTickets.values()).find(
    ticket => ticket.userId === userId && ticket.guildId === guildId
  );
}

// Delete ticket data from active tickets
async function deleteTicketData(channelId) {
  return activeTickets.delete(channelId);
}

// Cleanup function to remove orphaned tickets
async function cleanupOrphanedTickets(guild) {
  try {
    const guildTickets = Array.from(activeTickets.entries()).filter(
      ([channelId, ticket]) => ticket.guildId === guild.id
    );

    for (const [channelId, ticket] of guildTickets) {
      const channel = guild.channels.cache.get(channelId);
      if (!channel) {
        // Channel doesn't exist anymore, remove from active tickets
        activeTickets.delete(channelId);
        logger.info('Removed orphaned ticket data', {
          ticketId: ticket.id,
          channelId,
        });
      }
    }
  } catch (error) {
    logger.error('Error cleaning up orphaned tickets', {
      error: error.message,
      guildId: guild.id,
    });
  }
}

async function handleAssignmentSelect(interaction, config) {
  try {
    const assignedUserId = interaction.values[0];
    const ticketData = activeTickets.get(interaction.channelId);

    if (!ticketData) {
      await updateEphemeralStatus(
        interaction,
        '❌ Ticket data not found.',
        'error'
      );
      return;
    }

    const assignedMember = await interaction.guild.members.fetch(assignedUserId);

    // Update ticket assignment
    ticketData.assignedTo = assignedUserId;
    activeTickets.set(interaction.channelId, ticketData);

    // Update the ticket embed
    await updateTicketEmbed(interaction.channel, ticketData, config);

    await interaction.update({
      content: `✅ Ticket assigned to ${assignedMember}`,
      components: [],
    });

    logger.info('Ticket assigned', {
      ticketId: ticketData.id,
      assignedTo: assignedUserId,
      assignedBy: interaction.user.id,
    });
  } catch (error) {
    logger.error('Error in handleAssignmentSelect', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });
  }
}

async function handleStatusChange(interaction, config) {
  try {
    const newStatus = interaction.values[0];
    const ticketData = activeTickets.get(interaction.channelId);

    if (!ticketData) {
      await updateEphemeralStatus(
        interaction,
        '❌ Ticket data not found.',
        'error'
      );
      return;
    }

    // Update ticket status
    ticketData.status = newStatus;
    activeTickets.set(interaction.channelId, ticketData);

    const statusEmojis = {
      open: '🟢',
      in_progress: '🟡',
      pending: '🟠',
      resolved: '✅'
    };

    const statusNames = {
      open: 'Open',
      in_progress: 'In Progress',
      pending: 'Pending',
      resolved: 'Resolved'
    };

    // Update the ticket embed
    await updateTicketEmbed(interaction.channel, ticketData, config);

    await interaction.update({
      content: `${statusEmojis[newStatus]} Ticket status updated to **${statusNames[newStatus]}**`,
      components: [],
    });

    logger.info('Ticket status updated', {
      ticketId: ticketData.id,
      newStatus,
      userId: interaction.user.id,
    });
  } catch (error) {
    logger.error('Error in handleStatusChange', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.user.id,
      guildId: interaction.guildId,
    });
  }
}

// Export all functions at the end
export {
  handleCreateTicketButton,
  handleTicketCategorySelect,
  handleTicketButtonInteractions,
  handleTicketSelectMenuInteractions,
  handleClaimTicket,
  handleTransferTicket,
  handleTransferSelect,
  handleSetPriority,
  handleCloseTicket,
  handleConfirmCloseTicket,
  handleCancelCloseTicket,
  handleAddNoteButton,
  handleViewNotesButton,
  handlePrioritySelect,
  updateTicketEmbed,
  getTicketData,
  getAllActiveTickets,
  getUserActiveTicket,
  deleteTicketData,
  cleanupOrphanedTickets,
  handleAssignmentSelect,
  handleStatusChange,
  loadActiveTickets
};
