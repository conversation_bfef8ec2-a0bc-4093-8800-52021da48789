import { Client, ChannelType, MessageFlags } from 'discord.js';
import Logger from '../../utils/logger.js';
import { config } from './config.js';
import { cleanupEphemeralTracker } from './utils/discordUtils.js';
import { cleanupDbClient } from './utils/dbUtils.js';
import {
  handleRulesButton,
  handleRulesNavigation
} from './handlers/rules.js';
import {
  handleGetRolesButton,
  handleRoleSelect
} from './handlers/roles.js';
import {
  handleCreateSuggestionButton,
  handleSuggestionCategorySelect,
  handleSuggestionModalSubmit,
  handleSuggestionVote,
  handleSuggestionRejectModal,
  handleSuggestionSelect,
} from './handlers/suggestions.js';
import {
  handleGetSupportButton,
  handleSupportTypeSelect
} from './handlers/support.js';
import {
  handleCreateIntroductionButton,
  handleIntroductionInteraction
} from './handlers/introductions.js';
import {
  handleTicketCategorySelect,
  handleCreateTicketButton,
  handleClaimTicket,
  handleTransferTicket,
  handleTransferSelect,
  handleSetPriority,
  handleCloseTicket,
  handleConfirmCloseTicket,
  handleCancelCloseTicket,
  handlePrioritySelect,
  handleAssignmentSelect,
  handleStatusChange,
  loadActiveTickets
} from './handlers/tickets.js';
import {
  handleSuggestionButtons,
  handleSuggestionSelectMenus,
  handleTicketButtons,
  handleTicketSelectMenus,
  handleMainHubButtons,
  updateButtonCooldowns
} from './handlers/buttonHandler.js';
import { handleAddNote } from './utils/notesUtils.js';
import { startSLAMonitoring, stopSLAMonitoring } from './utils/slaUtils.js';
import suggestionsCommand from './commands/suggestions.js';
// import { setupSuggestionsChannelPermissions } from './utils/permissionUtils.js'; // Removed as file not found

// Create a logger instance
const logger = new Logger({ level: 'info' });

// SLA monitoring interval
let slaMonitoringInterval = null;

function buildServerHubContainer(guild) {
  const hubContent = config.settings.hubContent;
  const features = config.settings.features || {};
  const accentColor = parseInt(hubContent.accentColor || 'FF6B35', 16);

  // Prepare the text content
  const fullDescription = [
    hubContent.description,
    '',
    `**${hubContent.whatWeOffer.title}**`,
    hubContent.whatWeOffer.content,
    '',
    `**${hubContent.gettingStarted.title}**`,
    hubContent.gettingStarted.content,
    '',
    `**${hubContent.callToAction.title}**`,
    hubContent.callToAction.content,
  ].join('\n');

  // Prepare the container components
  const containerComponents = [
    // Title component
    {
      type: 10, // Text display
      content: `# ${hubContent.title}\n## ${hubContent.subtitle}`
    },
    // Description component
    {
      type: 10, // Text display
      content: fullDescription
    }
  ];

  // Create buttons based on enabled features
  const actionButtons = [];

  if (features.rules?.enabled !== false) {
    actionButtons.push({
      type: 2, // Button
      label: 'Server Rules',
      style: 4, // Danger style
      custom_id: 'server_rules',
      emoji: '📗'
    });
  }

  if (features.roles?.enabled !== false) {
    actionButtons.push({
      type: 2, // Button
      label: 'Get Roles',
      style: 1, // Primary style
      custom_id: 'get_roles',
      emoji: '🎭'
    });
  }

  if (features.introductions?.enabled !== false) {
    actionButtons.push({
      type: 2, // Button
      label: 'Introductions',
      style: 2, // Secondary style
      custom_id: 'create_introduction',
      emoji: '👋'
    });
  }

  if (features.tickets?.enabled !== false || features.suggestions?.enabled !== false) {
    actionButtons.push({
      type: 2, // Button
      label: 'Support',
      style: 3, // Success style
      custom_id: 'get_support',
      emoji: '🎫'
    });
  }

  // Add action row with buttons if there are any
  if (actionButtons.length > 0) {
    containerComponents.push({
      type: 1, // Action Row
      components: actionButtons
    });
  }

  // Add footer if exists
  if (hubContent.footer) {
    containerComponents.push({
      type: 10, // Text display
      content: `---\n${hubContent.footer}`
    });
  }

  // Create the full container
  return {
    type: 17, // Container
    accent_color: accentColor,
    components: containerComponents
  };
}

async function setupServerHub(client) {
  // Validate hubChannelId before attempting to fetch
  const hubChannelId = config.settings?.hubChannelId;

  // Log diagnostic information
  logger.info('Server Hub setup starting', {
    hubChannelId,
    botId: client.user?.id,
    guildCount: client.guilds.cache.size,
    guilds: client.guilds.cache.map(g => ({ 
      id: g.id, 
      name: g.name, 
      memberCount: g.memberCount,
      botInGuild: g.members.cache.has(client.user?.id)
    }))
  });

  // Validate hubChannelId
  if (!hubChannelId) {
    logger.error('Hub Channel ID is undefined or null', {
      fullConfig: JSON.stringify(config, null, 2)
    });
    throw new Error('Hub Channel ID is not configured');
  }

  try {
    // Ensure hubChannelId is a string and matches Discord's snowflake format
    if (typeof hubChannelId !== 'string' || !/^\d+$/.test(hubChannelId)) {
      logger.error('Invalid hub channel ID format', {
        hubChannelId: hubChannelId,
        type: typeof hubChannelId
      });
      throw new Error(`Invalid channel ID: ${hubChannelId}`);
    }

    // First try to get the channel from cache
    let channel = client.channels.cache.get(hubChannelId);
    
    if (!channel) {
      logger.info('Channel not in cache, attempting to fetch', { channelId: hubChannelId });
      try {
        channel = await client.channels.fetch(hubChannelId, { 
          force: true // Force a fresh API call
        });
      } catch (fetchError) {
        logger.error('Failed to fetch channel', { 
          channelId: hubChannelId,
          error: fetchError.message,
          code: fetchError.code,
          guildCount: client.guilds.cache.size,
          guilds: client.guilds.cache.map(g => ({ id: g.id, name: g.name }))
        });
        
        // Provide helpful error messages based on the error code
        if (fetchError.code === 50001) {
          throw new Error(`Missing access to channel ${hubChannelId}. Please ensure:\n1. The bot has permission to view this channel\n2. The bot is in the correct server\n3. The channel ID is correct`);
        } else if (fetchError.code === 10003) {
          throw new Error(`Channel ${hubChannelId} not found. Please check the channel ID in config.yml.`);
        } else {
          throw new Error(`Failed to access channel ${hubChannelId}: ${fetchError.message} (Code: ${fetchError.code})`);
        }
      }
    }

    if (!channel) {
      logger.error('Channel not found after fetch', { 
        channelId: hubChannelId
      });
      throw new Error(`Channel with ID ${hubChannelId} not found`);
    }

    // logger.info('Channel fetched successfully', { 
    //   channelId: channel.id, 
    //   channelName: channel.name, 
    //   channelType: channel.type,
    //   guildId: channel.guild?.id,
    //   guildName: channel.guild?.name
    // });

    if (!channel || channel.type !== ChannelType.GuildText) {
      logger.error('Invalid hub channel configuration', { 
        channelId: config.settings.hubChannelId,
        channelExists: !!channel,
        channelType: channel?.type 
      });
      return;
    }

    const container = buildServerHubContainer(channel.guild);
    // logger.info('Container built successfully', { 
    //   containerType: container.type,
    //   componentCount: container.components.length
    // });

    const messageData = {
      components: [container],
      flags: MessageFlags.SuppressEmbeds | (1 << 15) // IS_COMPONENTS_V2 flag
    };

    const messages = await channel.messages.fetch({ limit: 10 });
    // logger.info('Messages fetched', { messageCount: messages.size });

    const existingMessage = messages.find(
      (msg) =>
        client.user &&
        msg.author.id === client.user.id &&
        msg.components.length > 0 &&
        msg.components[0]?.type === 17
    );

    if (existingMessage) {
      try {
        await existingMessage.edit(messageData);
        // logger.info('Updated existing hub message');
      } catch (editError) {
        logger.warn('Failed to edit hub message, recreating', { error: editError });
        await existingMessage.delete();
        await channel.send(messageData);
        // logger.info('Recreated hub message after edit failure');
      }
    } else {
      await channel.send(messageData);
      // logger.info('Created new hub message');
    }

    // Setup suggestions channel permissions (disabled as permissionUtils.js is missing)
    /*
    if (config.settings.features.suggestions?.enabled && config.settings.features.suggestions?.channelId) {
      await setupSuggestionsChannelPermissions(client, config.settings.features.suggestions.channelId);
    }
    */
  } catch (error) {
    if (error instanceof Error && error.code === 10003) { // DiscordAPIError[10003]: Unknown Channel
      logger.error('Failed to set up hub message: Configured channel not found or inaccessible. Please check your hubChannelId in config.yml.', {
        hubChannelId: hubChannelId,
        error: error.message
      });
      return; // Prevent further execution if channel is not found
    }

    logger.error('Failed to setup hub message', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace'
    });
    throw error;
  }
}

export default {
  info: {
    name: config.addon.name,
    version: config.addon.version,
    description: config.addon.description,
    author: config.addon.author
  },

  commands: [suggestionsCommand],

  events: [
    {
      name: 'interactionCreate',
      execute: async (interaction) => {
        try {
          if (interaction.isChatInputCommand()) {
            // Handle suggestion command
            if (interaction.commandName === 'suggestions') {
              await suggestionsCommand.execute(interaction);
              return;
            }

          }

          if (interaction.isButton()) {
            // First, check if this is a main hub button that needs cooldown handling
            const mainHubHandled = await handleMainHubButtons(interaction, config);
            if (mainHubHandled) {
              return; // Button was handled by cooldown system
            }

            // Handle suggestion vote buttons
            if (interaction.customId.startsWith('suggestion_vote_')) {
              const parts = interaction.customId.split('_');
              const voteType = parts[2];
              const suggestionNumberStr = parts[3];
              
              // Ensure suggestion number is a valid number
              const suggestionNumber = parseInt(suggestionNumberStr, 10);
              
              logger.info('Processing suggestion vote button', {
                customId: interaction.customId,
                parts,
                voteType,
                suggestionNumberStr,
                suggestionNumber,
                isValidNumber: !isNaN(suggestionNumber)
              });

              if (isNaN(suggestionNumber)) {
                logger.error('Invalid suggestion number', {
                  customId: interaction.customId,
                  suggestionNumberStr
                });

                await interaction.reply({
                  content: '❌ Invalid suggestion number. Please contact support.',
                  ephemeral: true
                });
                return;
              }

              await handleSuggestionVote(interaction, suggestionNumber, voteType);
              return;
            }

            // Handle suggestion buttons
            if (interaction.customId.startsWith('suggestion_') || interaction.customId === 'suggestions_back') {
              await handleSuggestionButtons(interaction, config);
              return;
            }

            // Handle ticket buttons using the centralized handler
            if (interaction.customId.startsWith('ticket_') || 
                interaction.customId === 'ticket_claim' ||
                interaction.customId === 'ticket_set_priority' ||
                interaction.customId === 'ticket_transfer' ||
                interaction.customId === 'ticket_close') {
              await handleTicketButtons(interaction, config);
              return;
            }

            // Handle introduction builder interactions
            if (interaction.customId.startsWith('introduction_')) {
              const handled = await handleIntroductionInteraction(interaction);
              if (handled) return;
            }

            // Handle other buttons
            if (interaction.customId === 'server_rules') {
              await handleRulesButton(interaction, config);
            } else if (interaction.customId.startsWith('rules_')) {
              // Handle rules navigation buttons
              await handleRulesNavigation(interaction, config);
            } else if (interaction.customId === 'get_roles') {
              await handleGetRolesButton(interaction, config);
            } else if (interaction.customId === 'get_support') {
              await handleGetSupportButton(interaction, config);
            } else if (interaction.customId === 'create_introduction') {
              await handleCreateIntroductionButton(interaction, config);
            } else if (interaction.customId === 'create_ticket') {
              await handleCreateTicketButton(interaction, config);
            } else if (interaction.customId === 'create_suggestion') {
              await handleCreateSuggestionButton(interaction, config);
            }
          } else if (interaction.isStringSelectMenu()) {
            // Handle suggestion select menus
            if (interaction.customId === 'suggestion_select') {
              await handleSuggestionSelect(interaction);
              return;
            }

            // Handle ticket select menus using the centralized handler
            if (interaction.customId.startsWith('ticket_') || 
                interaction.customId === 'ticket_category_select') {
              await handleTicketSelectMenus(interaction, config);
              return;
            }

            if (interaction.customId === 'support_type_select') {
              await handleSupportTypeSelect(interaction, config);
            } else if (interaction.customId === 'suggestion_category_select') {
              await handleSuggestionCategorySelect(interaction, config);
            } else if (interaction.customId.startsWith('role_select_')) {
              await handleRoleSelect(interaction, config);
            } else if (interaction.customId.startsWith('introduction_')) {
              // Handle introduction select menus
              const handled = await handleIntroductionInteraction(interaction);
              if (!handled) {
                logger.warn('Unhandled introduction select menu', { customId: interaction.customId });
              }
            }
          } else if (interaction.isModalSubmit()) {
            if (interaction.customId.startsWith('suggestion_modal_')) {
              await handleSuggestionModalSubmit(interaction, config);
            } else if (interaction.customId.startsWith('suggestion_reject_modal_')) {
              const suggestionNumber = parseInt(interaction.customId.replace('suggestion_reject_modal_', ''));
              await handleSuggestionRejectModal(interaction, suggestionNumber);
            } else if (interaction.customId.startsWith('add_note_')) {
              await handleAddNote(interaction, config);
            } else if (interaction.customId.includes('_modal_')) {
              // Handle introduction modal submissions
              const handled = await handleIntroductionInteraction(interaction);
              if (!handled) {
                logger.warn('Unhandled modal submission', { customId: interaction.customId });
              }
            }
          }
        } catch (error) {
          logger.error('Failed to handle interaction', {
            error: error instanceof Error ? error.message : 'Unknown error',
            customId: interaction.customId,
            type: interaction.type,
            stack: error instanceof Error ? error.stack : 'No stack trace'
          });
        }
      }
    }
  ],

  async onLoad(bot) {
    const { client, logger: addonLogger } = bot;

    addonLogger.info('Server Hub addon loading', { 
      clientReady: client.isReady(), 
      clientUser: client.user?.username 
    });

    // Update button cooldowns from config
    updateButtonCooldowns(config);
    addonLogger.info('Button cooldowns updated from config');

    // If client is already ready, call setupServerHub immediately
    if (client.isReady()) {
      try {
        await setupServerHub(client);
        addonLogger.info('Server Hub setup completed immediately');
      } catch (error) {
        addonLogger.error('Failed to setup server hub immediately', {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : 'No stack trace'
        });
      }
    }

    // Always attach the ready event listener
    client.once('ready', async () => {
      try {
        await setupServerHub(client);
        addonLogger.info('Server Hub setup completed on ready event');
       
        // Load active tickets for each guild the bot is in
        for (const guild of client.guilds.cache.values()) {
          const loadedTicketCount = await loadActiveTickets(guild);
          addonLogger.info(`Loaded ${loadedTicketCount} active tickets for guild ${guild.name}`);
        }

        // Start SLA monitoring
        slaMonitoringInterval = startSLAMonitoring(client, config);
        addonLogger.info('SLA monitoring started for server hub');
      } catch (error) {
        addonLogger.error('Failed to setup server hub', {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : 'No stack trace'
        });
      }
    });

    return true;
  },

  async onUnload() {
    // Stop SLA monitoring
    if (slaMonitoringInterval) {
      stopSLAMonitoring(slaMonitoringInterval);
      slaMonitoringInterval = null;
    }
    
    await cleanupEphemeralTracker();
    await cleanupDbClient();
  }
};