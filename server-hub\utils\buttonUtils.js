import { <PERSON><PERSON>ow<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } from 'discord.js';
import Logger from '../../../utils/logger.js';

// Create a logger instance
const logger = new Logger({ level: 'info' });

/**
 * Create a disabled button row to replace the original buttons after use
 * @param {Array} originalButtons - Array of original button configurations
 * @param {string} reason - Reason for disabling (e.g., "Used", "Cooldown")
 * @returns {ActionRowBuilder} - Action row with disabled buttons
 */
export function createDisabledButtonRow(originalButtons, reason = 'Used') {
  const disabledRow = new ActionRowBuilder();
  
  originalButtons.forEach(buttonConfig => {
    const disabledButton = new ButtonBuilder()
      .setCustomId(buttonConfig.custom_id)
      .setLabel(`${buttonConfig.label} (${reason})`)
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(true);
    
    if (buttonConfig.emoji) {
      disabledButton.setEmoji(buttonConfig.emoji);
    }
    
    disabledRow.addComponents(disabledButton);
  });
  
  return disabledRow;
}

/**
 * Create a temporary disabled state for buttons
 * @param {Array} originalButtons - Array of original button configurations
 * @param {number} duration - Duration in milliseconds to keep disabled
 * @returns {Object} - Object containing disabled row and re-enable function
 */
export function createTemporaryDisabledButtons(originalButtons, duration = 5000) {
  const disabledRow = createDisabledButtonRow(originalButtons, 'Cooldown');
  
  // Function to re-enable buttons after duration
  const reEnableButtons = () => {
    return createEnabledButtonRow(originalButtons);
  };
  
  // Auto-re-enable after duration
  setTimeout(() => {
    logger.debug('Auto-re-enabling buttons after cooldown');
  }, duration);
  
  return {
    disabledRow,
    reEnableButtons,
    duration
  };
}

/**
 * Create an enabled button row from button configurations
 * @param {Array} buttonConfigs - Array of button configurations
 * @returns {ActionRowBuilder} - Action row with enabled buttons
 */
export function createEnabledButtonRow(buttonConfigs) {
  const row = new ActionRowBuilder();
  
  buttonConfigs.forEach(buttonConfig => {
    const button = new ButtonBuilder()
      .setCustomId(buttonConfig.custom_id)
      .setLabel(buttonConfig.label)
      .setStyle(buttonConfig.style || ButtonStyle.Primary);
    
    if (buttonConfig.emoji) {
      button.setEmoji(buttonConfig.emoji);
    }
    
    row.addComponents(button);
  });
  
  return row;
}

/**
 * Check if a button interaction should be allowed based on user permissions
 * @param {Object} interaction - Discord interaction object
 * @param {Array} allowedRoles - Array of role IDs that are allowed
 * @param {boolean} requireRoles - Whether roles are required (default: false)
 * @returns {Object} - Object with allowed status and reason
 */
export function checkButtonPermissions(interaction, allowedRoles = [], requireRoles = false) {
  // If no roles specified and not required, allow all
  if (allowedRoles.length === 0 && !requireRoles) {
    return { allowed: true, reason: 'No restrictions' };
  }
  
  // Check if user has any of the allowed roles
  const userRoles = interaction.member?.roles?.cache;
  if (!userRoles) {
    return { allowed: false, reason: 'Unable to check user roles' };
  }
  
  const hasAllowedRole = allowedRoles.some(roleId => userRoles.has(roleId));
  
  if (requireRoles && !hasAllowedRole) {
    return { allowed: false, reason: 'Required role not found' };
  }
  
  if (allowedRoles.length > 0 && !hasAllowedRole) {
    return { allowed: false, reason: 'Insufficient permissions' };
  }
  
  return { allowed: true, reason: 'Permission granted' };
}

/**
 * Create a button with proper state management
 * @param {Object} config - Button configuration
 * @param {boolean} disabled - Whether button should be disabled
 * @param {string} disabledReason - Reason for disabling
 * @returns {ButtonBuilder} - Configured button
 */
export function createManagedButton(config, disabled = false, disabledReason = '') {
  const button = new ButtonBuilder()
    .setCustomId(config.custom_id)
    .setLabel(disabled ? `${config.label} (${disabledReason})` : config.label)
    .setStyle(disabled ? ButtonStyle.Secondary : (config.style || ButtonStyle.Primary))
    .setDisabled(disabled);
  
  if (config.emoji) {
    button.setEmoji(config.emoji);
  }
  
  return button;
}

/**
 * Log button interaction for analytics
 * @param {Object} interaction - Discord interaction object
 * @param {string} buttonId - Button identifier
 * @param {string} action - Action performed
 */
export function logButtonInteraction(interaction, buttonId, action = 'clicked') {
  logger.info(`✅ ${interaction.user.username} → ${buttonId} (${action})`, {
    userId: interaction.user.id,
    buttonId,
    action,
    timestamp: new Date().toISOString()
  });
}
