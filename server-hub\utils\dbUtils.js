import { MongoClient } from 'mongodb';
import { config } from '../config.js';

let cachedClient = null;

export async function getTicketsDb() {
  if (!cachedClient) {
    const mongoUri = config.database?.mongodb?.url;
    const dbName = config.database?.mongodb?.dbName || 'discordbot';
    
    if (!mongoUri) {
      throw new Error('MongoDB connection URL is not configured');
    }

    cachedClient = await MongoClient.connect(mongoUri);
  }
  return cachedClient.db(config.database.mongodb.dbName).collection('tickets');
}

export async function getSuggestionsDb() {
  if (!cachedClient) {
    const mongoUri = config.database?.mongodb?.url;
    const dbName = config.database?.mongodb?.dbName || 'discordbot';
    
    if (!mongoUri) {
      throw new Error('MongoDB connection URL is not configured');
    }

    cachedClient = await MongoClient.connect(mongoUri);
  }
  return cachedClient.db(config.database.mongodb.dbName).collection('suggestions');
}

export async function getApplicationsDb() {
  if (!cachedClient) {
    const mongoUri = config.database?.mongodb?.url;
    const dbName = config.database?.mongodb?.dbName || 'discordbot';
    
    if (!mongoUri) {
      throw new Error('MongoDB connection URL is not configured');
    }

    cachedClient = await MongoClient.connect(mongoUri);
  }
  return cachedClient.db(config.database.mongodb.dbName).collection('applications');
}

export async function cleanupDbClient() {
  if (cachedClient) {
    await cachedClient.close();
    cachedClient = null;
  }
} 