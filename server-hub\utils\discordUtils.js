const ephemeralTracker = new Map();

export function scheduleEphemeralDeletion(
  interactionId,
  interaction,
  delay = 60000
) {
  const existingTimeout = ephemeralTracker.get(interactionId);
  if (existingTimeout) {
    clearTimeout(existingTimeout);
  }

  const timeout = setTimeout(async () => {
    try {
      await interaction.deleteReply();
      ephemeralTracker.delete(interactionId);
    } catch (error) {
      ephemeralTracker.delete(interactionId);
    }
  }, delay);

  ephemeralTracker.set(interactionId, timeout);
}

export async function updateEphemeralStatus(
  interaction,
  status,
  color = 'success'
) {
  const emoji = color === 'success' ? '✅' : color === 'warning' ? '⚠️' : '❌';
  try {
    await interaction.editReply({
      content: `${emoji} ${status}`,
    });
  } catch (error) {
    // Ignore if interaction is no longer valid
  }
}

export async function cleanupEphemeralTracker() {
  for (const [id, timeout] of ephemeralTracker.entries()) {
    clearTimeout(timeout);
    ephemeralTracker.delete(id);
  }
} 