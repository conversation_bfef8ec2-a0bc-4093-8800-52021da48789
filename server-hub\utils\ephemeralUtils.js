import { EmbedBuilder } from 'discord.js';
import logger from '../../../utils/logger.js';

// Track ephemeral messages for cleanup
const ephemeralMessages = new Map();

export async function scheduleEphemeralDeletion(interactionId, interaction, delay = 60000) {
  try {
    // Store the interaction for potential later deletion
    ephemeralMessages.set(interactionId, interaction);

    // Schedule automatic deletion
    setTimeout(async () => {
      try {
        if (ephemeralMessages.has(interactionId)) {
          await interaction.deleteReply().catch(() => {});
          ephemeralMessages.delete(interactionId);
        }
      } catch (error) {
        logger.warn('Failed to delete ephemeral message', {
          error: error instanceof Error ? error.message : 'Unknown error',
          interactionId,
        });
      }
    }, delay);
  } catch (error) {
    logger.error('Error scheduling ephemeral deletion', {
      error: error instanceof Error ? error.message : 'Unknown error',
      interactionId,
    });
  }
}

export async function updateEphemeralStatus(
  interaction, 
  message, 
  type = 'success', 
  delay = 15000
) {
  try {
    const colors = {
      success: 0x00ff00,  // Green
      error: 0xff0000,    // Red
      warning: 0xff9900,  // Orange
      info: 0x5865f2      // Discord Blurple
    };

    const embed = new EmbedBuilder()
      .setDescription(message)
      .setColor(colors[type] || colors.info)
      .setTimestamp();

    if (interaction.deferred || interaction.replied) {
      await interaction.editReply({ embeds: [embed] });
    } else {
      await interaction.reply({ 
        embeds: [embed], 
        ephemeral: true 
      });
    }

    // Schedule deletion
    scheduleEphemeralDeletion(interaction.id, interaction, delay);
  } catch (error) {
    logger.error('Error updating ephemeral status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      message,
      type,
    });
  }
}

export async function cleanupEphemeralTracker() {
  try {
    for (const [interactionId, interaction] of ephemeralMessages.entries()) {
      try {
        await interaction.deleteReply().catch(() => {});
      } catch (error) {
        logger.warn('Failed to delete ephemeral message during cleanup', {
          error: error instanceof Error ? error.message : 'Unknown error',
          interactionId,
        });
      }
      ephemeralMessages.delete(interactionId);
    }
  } catch (error) {
    logger.error('Error during ephemeral tracker cleanup', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
} 