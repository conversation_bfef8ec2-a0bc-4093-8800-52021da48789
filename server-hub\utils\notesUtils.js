import { Em<PERSON><PERSON><PERSON><PERSON>, ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } from 'discord.js';
import Logger from '../../../utils/logger.js';
import { getTicketsDb } from './dbUtils.js';

const logger = new Logger({ level: 'info' });

/**
 * Show modal for adding a staff note
 * @param {Object} interaction - Discord interaction
 * @param {string} ticketId - Ticket ID
 */
export async function showAddNoteModal(interaction, ticketId) {
  const modal = new ModalBuilder()
    .setCustomId(`add_note_${ticketId}`)
    .setTitle('Add Staff Note');

  const noteInput = new TextInputBuilder()
    .setCustomId('note_content')
    .setLabel('Internal Note')
    .setStyle(TextInputStyle.Paragraph)
    .setPlaceholder('Add internal staff note (not visible to ticket creator)...')
    .setRequired(true)
    .setMaxLength(2000);

  const actionRow = new ActionRowBuilder().addComponents(noteInput);
  modal.addComponents(actionRow);

  await interaction.showModal(modal);
}

/**
 * Handle adding a staff note
 * @param {Object} interaction - Discord interaction
 * @param {Object} config - Configuration
 */
export async function handleAddNote(interaction, config) {
  try {
    const ticketId = interaction.customId.replace('add_note_', '');
    const noteContent = interaction.fields.getTextInputValue('note_content');

    // Check if user has staff permissions
    const configStaffRoles = 
      config?.settings?.features?.tickets?.staffRoles || 
      config?.settings?.staffRoles || 
      [];

    const hasStaffRole = configStaffRoles.some(roleId => 
      interaction.member.roles.cache.has(roleId)
    );

    if (!hasStaffRole) {
      await interaction.reply({
        content: '❌ Only staff members can add internal notes.',
        ephemeral: true
      });
      return;
    }

    // Add note to database
    const ticketsCollection = await getTicketsDb();
    const note = {
      id: Math.random().toString(36).substring(2, 11).toUpperCase(),
      authorId: interaction.user.id,
      authorName: interaction.user.displayName || interaction.user.username,
      content: noteContent,
      createdAt: new Date(),
      edited: false
    };

    await ticketsCollection.updateOne(
      { id: ticketId },
      { 
        $push: { notes: note },
        $set: { updatedAt: new Date() }
      }
    );

    // Create note embed for staff channel
    const noteEmbed = new EmbedBuilder()
      .setTitle(`📝 Staff Note Added - Ticket #${ticketId}`)
      .setDescription(noteContent)
      .addFields(
        { name: 'Added by', value: `<@${interaction.user.id}>`, inline: true },
        { name: 'Channel', value: `<#${interaction.channelId}>`, inline: true },
        { name: 'Time', value: `<t:${Math.floor(Date.now() / 1000)}:R>`, inline: true }
      )
      .setColor(0x3B82F6)
      .setTimestamp();

    // Send note to staff log channel if configured
    if (config.settings.features.tickets.logChannel) {
      const logChannel = interaction.guild.channels.cache.get(config.settings.features.tickets.logChannel);
      if (logChannel) {
        await logChannel.send({ embeds: [noteEmbed] });
      }
    }

    await interaction.reply({
      content: '✅ Staff note added successfully.',
      ephemeral: true
    });

    logger.info('Staff note added', {
      ticketId,
      noteId: note.id,
      authorId: interaction.user.id,
      channelId: interaction.channelId
    });

  } catch (error) {
    logger.error('Failed to add staff note', {
      error: error.message,
      ticketId: interaction.customId.replace('add_note_', ''),
      userId: interaction.user.id
    });

    await interaction.reply({
      content: '❌ Failed to add staff note. Please try again.',
      ephemeral: true
    });
  }
}

/**
 * Get all notes for a ticket
 * @param {string} ticketId - Ticket ID
 * @returns {Array} Array of notes
 */
export async function getTicketNotes(ticketId) {
  try {
    const ticketsCollection = await getTicketsDb();
    const ticket = await ticketsCollection.findOne({ id: ticketId });
    return ticket?.notes || [];
  } catch (error) {
    logger.error('Failed to get ticket notes', {
      error: error.message,
      ticketId
    });
    return [];
  }
}

/**
 * Show ticket notes to staff
 * @param {Object} interaction - Discord interaction
 * @param {string} ticketId - Ticket ID
 */
export async function showTicketNotes(interaction, ticketId) {
  try {
    const notes = await getTicketNotes(ticketId);

    if (notes.length === 0) {
      await interaction.reply({
        content: '📝 No staff notes found for this ticket.',
        ephemeral: true
      });
      return;
    }

    const notesEmbed = new EmbedBuilder()
      .setTitle(`📝 Staff Notes - Ticket #${ticketId}`)
      .setColor(0x3B82F6)
      .setTimestamp();

    // Add notes as fields (limit to 10 most recent)
    const recentNotes = notes.slice(-10);
    recentNotes.forEach((note, index) => {
      notesEmbed.addFields({
        name: `Note ${index + 1} - ${note.authorName}`,
        value: `${note.content}\n*<t:${Math.floor(new Date(note.createdAt).getTime() / 1000)}:R>*`,
        inline: false
      });
    });

    if (notes.length > 10) {
      notesEmbed.setFooter({ text: `Showing 10 most recent notes. Total: ${notes.length}` });
    }

    await interaction.reply({
      embeds: [notesEmbed],
      ephemeral: true
    });

  } catch (error) {
    logger.error('Failed to show ticket notes', {
      error: error.message,
      ticketId,
      userId: interaction.user.id
    });

    await interaction.reply({
      content: '❌ Failed to load ticket notes.',
      ephemeral: true
    });
  }
} 