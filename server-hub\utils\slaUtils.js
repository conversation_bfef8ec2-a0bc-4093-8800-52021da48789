import { EmbedBuilder } from 'discord.js';
import Logger from '../../../utils/logger.js';
import { getTicketsDb } from './dbUtils.js';

const logger = new Logger({ level: 'info' });

// SLA thresholds in milliseconds - based on ticket category response times
const SLA_THRESHOLDS = {
  urgent: 15 * 60 * 1000,     // 15 minutes (user_report)
  high: 30 * 60 * 1000,       // 30 minutes (nsfw_support) / 1 hour (technical_support)
  normal: 4 * 60 * 60 * 1000, // 4 hours (general_support)
  low: 24 * 60 * 60 * 1000    // 24 hours (fallback)
};

/**
 * Check if a ticket is overdue based on SLA
 * @param {Object} ticketData - Ticket data
 * @returns {boolean} Whether ticket is overdue
 */
export function isTicketOverdue(ticketData) {
  const now = new Date();
  const createdAt = new Date(ticketData.createdAt);
  const priority = ticketData.priority || 'normal';
  
  // Special handling for technical support (1 hour response time)
  let threshold;
  if (ticketData.category === 'technical_support') {
    threshold = 1 * 60 * 60 * 1000; // 1 hour
  } else {
    threshold = SLA_THRESHOLDS[priority] || SLA_THRESHOLDS.normal;
  }
  
  // If ticket is claimed or in progress, use different threshold
  if (ticketData.status === 'in_progress' || ticketData.claimedBy) {
    return (now - createdAt) > (threshold * 2); // Double the time for in-progress tickets
  }
  
  return (now - createdAt) > threshold;
}

/**
 * Get time remaining until SLA breach
 * @param {Object} ticketData - Ticket data
 * @returns {number} Milliseconds until SLA breach (negative if already breached)
 */
export function getTimeUntilSLABreach(ticketData) {
  const now = new Date();
  const createdAt = new Date(ticketData.createdAt);
  const priority = ticketData.priority || 'normal';
  
  // Special handling for technical support (1 hour response time)
  let threshold;
  if (ticketData.category === 'technical_support') {
    threshold = 1 * 60 * 60 * 1000; // 1 hour
  } else {
    threshold = SLA_THRESHOLDS[priority] || SLA_THRESHOLDS.normal;
  }
  
  const elapsed = now - createdAt;
  return threshold - elapsed;
}

/**
 * Format time duration for display
 * @param {number} milliseconds - Duration in milliseconds
 * @returns {string} Formatted duration
 */
export function formatDuration(milliseconds) {
  const absMs = Math.abs(milliseconds);
  const hours = Math.floor(absMs / (1000 * 60 * 60));
  const minutes = Math.floor((absMs % (1000 * 60 * 60)) / (1000 * 60));
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  return `${minutes}m`;
}

/**
 * Check all active tickets for SLA breaches and escalate if needed
 * @param {Object} client - Discord client
 * @param {Object} config - Configuration
 */
export async function checkSLABreaches(client, config) {
  try {
    const ticketsCollection = await getTicketsDb();
    const activeTickets = await ticketsCollection.find({
      status: { $in: ['open', 'in_progress', 'pending'] }
    }).toArray();

    const overdueTickets = [];
    const soonOverdueTickets = [];

    for (const ticket of activeTickets) {
      const timeUntilBreach = getTimeUntilSLABreach(ticket);
      
      if (timeUntilBreach < 0) {
        // Already overdue
        overdueTickets.push({
          ...ticket,
          overdueBy: Math.abs(timeUntilBreach)
        });
      } else if (timeUntilBreach < (15 * 60 * 1000)) {
        // Will be overdue in 15 minutes
        soonOverdueTickets.push({
          ...ticket,
          timeRemaining: timeUntilBreach
        });
      }
    }

    // Send escalation notifications
    if (overdueTickets.length > 0 || soonOverdueTickets.length > 0) {
      await sendSLANotifications(client, config, overdueTickets, soonOverdueTickets);
    }

    logger.info('SLA check completed', {
      totalTickets: activeTickets.length,
      overdueTickets: overdueTickets.length,
      soonOverdueTickets: soonOverdueTickets.length
    });

  } catch (error) {
    logger.error('Failed to check SLA breaches', {
      error: error.message,
      stack: error.stack
    });
  }
}

/**
 * Send SLA breach notifications
 * @param {Object} client - Discord client
 * @param {Object} config - Configuration
 * @param {Array} overdueTickets - Overdue tickets
 * @param {Array} soonOverdueTickets - Soon to be overdue tickets
 */
async function sendSLANotifications(client, config, overdueTickets, soonOverdueTickets) {
  const logChannelId = config.settings?.features?.tickets?.logChannel;
  if (!logChannelId) return;

  const logChannel = client.channels.cache.get(logChannelId);
  if (!logChannel) return;

  // Create overdue tickets embed
  if (overdueTickets.length > 0) {
    const overdueEmbed = new EmbedBuilder()
      .setTitle('🚨 SLA Breach Alert - Overdue Tickets')
      .setColor(0xFF0000)
      .setTimestamp()
      .setDescription(`${overdueTickets.length} ticket(s) have breached their SLA threshold.`);

    overdueTickets.slice(0, 10).forEach(ticket => {
      const guild = client.guilds.cache.get(ticket.guildId);
      const channel = guild?.channels.cache.get(ticket.channelId);
      
      overdueEmbed.addFields({
        name: `Ticket #${ticket.id} - ${ticket.priority.toUpperCase()}`,
        value: `${channel ? `<#${channel.id}>` : 'Channel not found'}\nOverdue by: ${formatDuration(ticket.overdueBy)}\nStatus: ${ticket.status}`,
        inline: true
      });
    });

    if (overdueTickets.length > 10) {
      overdueEmbed.setFooter({ text: `Showing 10 of ${overdueTickets.length} overdue tickets` });
    }

    await logChannel.send({ embeds: [overdueEmbed] });
  }

  // Create soon overdue tickets embed
  if (soonOverdueTickets.length > 0) {
    const soonOverdueEmbed = new EmbedBuilder()
      .setTitle('⚠️ SLA Warning - Tickets Approaching Deadline')
      .setColor(0xFF6B00)
      .setTimestamp()
      .setDescription(`${soonOverdueTickets.length} ticket(s) will breach SLA within 15 minutes.`);

    soonOverdueTickets.slice(0, 10).forEach(ticket => {
      const guild = client.guilds.cache.get(ticket.guildId);
      const channel = guild?.channels.cache.get(ticket.channelId);
      
      soonOverdueEmbed.addFields({
        name: `Ticket #${ticket.id} - ${ticket.priority.toUpperCase()}`,
        value: `${channel ? `<#${channel.id}>` : 'Channel not found'}\nTime remaining: ${formatDuration(ticket.timeRemaining)}\nStatus: ${ticket.status}`,
        inline: true
      });
    });

    if (soonOverdueTickets.length > 10) {
      soonOverdueEmbed.setFooter({ text: `Showing 10 of ${soonOverdueTickets.length} tickets approaching deadline` });
    }

    await logChannel.send({ embeds: [soonOverdueEmbed] });
  }
}

/**
 * Get SLA status for a specific ticket
 * @param {Object} ticketData - Ticket data
 * @returns {Object} SLA status information
 */
export function getTicketSLAStatus(ticketData) {
  const timeUntilBreach = getTimeUntilSLABreach(ticketData);
  const isOverdue = timeUntilBreach < 0;
  const priority = ticketData.priority || 'normal';
  
  let status = 'on-time';
  let color = 0x10B981; // Green
  let emoji = '🟢';
  
  if (isOverdue) {
    status = 'overdue';
    color = 0xFF0000; // Red
    emoji = '🔴';
  } else if (timeUntilBreach < (30 * 60 * 1000)) { // 30 minutes
    status = 'warning';
    color = 0xFF6B00; // Orange
    emoji = '🟡';
  }
  
  return {
    status,
    color,
    emoji,
    timeUntilBreach,
    isOverdue,
    priority,
    formattedTime: formatDuration(Math.abs(timeUntilBreach))
  };
}

/**
 * Start SLA monitoring interval
 * @param {Object} client - Discord client
 * @param {Object} config - Configuration
 * @returns {NodeJS.Timeout} Interval ID
 */
export function startSLAMonitoring(client, config) {
  // Check every 5 minutes
  const intervalId = setInterval(() => {
    checkSLABreaches(client, config);
  }, 5 * 60 * 1000);
  
  logger.info('SLA monitoring started', {
    checkInterval: '5 minutes'
  });
  
  return intervalId;
}

/**
 * Stop SLA monitoring
 * @param {NodeJS.Timeout} intervalId - Interval ID to clear
 */
export function stopSLAMonitoring(intervalId) {
  if (intervalId) {
    clearInterval(intervalId);
    logger.info('SLA monitoring stopped');
  }
} 