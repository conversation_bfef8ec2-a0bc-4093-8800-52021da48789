import { LRUCache } from 'lru-cache';
import { getSuggestionsDb } from './dbUtils.js';
import Logger from '../../../utils/logger.js';

const logger = new Logger({ level: 'info' });

// Create an LRU cache for suggestions
const suggestionCache = new LRUCache({
  max: 500, // Maximum number of suggestions to cache
  maxAge: 5 * 60 * 1000 // Cache for 5 minutes
});

// Batch update queue
const updateQueue = new Map();
let updateTimeout = null;

export async function getSuggestionFromCache(suggestionNumber, guildId) {
  const cacheKey = `${guildId}-${suggestionNumber}`;
  
  // Check cache first
  const cachedSuggestion = suggestionCache.get(cacheKey);
  if (cachedSuggestion) {
    logger.info('Suggestion retrieved from cache', { suggestionNumber, guildId });
    return cachedSuggestion;
  }

  // If not in cache, fetch from database
  const suggestionsDb = await getSuggestionsDb();
  
  // Try multiple query methods
  const suggestionQueries = [
    { suggestionNumber, guildId },
    { suggestionNumber: suggestionNumber.toString(), guildId },
    { suggestionNumber: suggestionNumber.toString() }
  ];

  let suggestion = null;
  for (const query of suggestionQueries) {
    const foundSuggestion = await suggestionsDb.findOne(query);
    if (foundSuggestion) {
      suggestion = foundSuggestion;
      break;
    }
  }

  // Cache the suggestion if found
  if (suggestion) {
    suggestionCache.set(cacheKey, suggestion);
  }

  return suggestion;
}

export function queueSuggestionUpdate(suggestionNumber, guildId, updateData) {
  const cacheKey = `${guildId}-${suggestionNumber}`;
  
  // Add to update queue
  updateQueue.set(cacheKey, updateData);

  // Clear any existing timeout
  if (updateTimeout) {
    clearTimeout(updateTimeout);
  }

  // Set a new timeout to batch updates
  updateTimeout = setTimeout(async () => {
    try {
      await batchUpdateSuggestions();
    } catch (error) {
      logger.error('Error in batch suggestion updates', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }, 500); // 500ms debounce
}

async function batchUpdateSuggestions() {
  if (updateQueue.size === 0) return;

  const suggestionsDb = await getSuggestionsDb();
  const updates = Array.from(updateQueue.entries());

  // Perform batch update
  const bulkOperations = updates.map(([cacheKey, updateData]) => {
    const [guildId, suggestionNumber] = cacheKey.split('-');
    
    // Update cache
    suggestionCache.set(cacheKey, {
      ...suggestionCache.get(cacheKey),
      ...updateData
    });

    // Prepare database update
    return {
      updateOne: {
        filter: { 
          suggestionNumber: parseInt(suggestionNumber), 
          guildId 
        },
        update: { $set: updateData }
      }
    };
  });

  // Perform bulk write
  if (bulkOperations.length > 0) {
    await suggestionsDb.bulkWrite(bulkOperations);
    logger.info('Batch updated suggestions', { 
      updateCount: bulkOperations.length 
    });
  }

  // Clear the update queue
  updateQueue.clear();
}

export function invalidateSuggestionCache(suggestionNumber, guildId) {
  const cacheKey = `${guildId}-${suggestionNumber}`;
  suggestionCache.delete(cacheKey);
}

export function clearSuggestionCache() {
  suggestionCache.clear();
} 