import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, <PERSON><PERSON><PERSON><PERSON>er, ButtonStyle } from 'discord.js';
import Logger from '../../../utils/logger.js';

const logger = new Logger({ level: 'info' });

/**
 * Get template configuration for a ticket category
 * @param {string} categoryId - Category ID
 * @param {Object} config - Configuration object
 * @returns {Object} Template configuration
 */
export function getTicketTemplate(categoryId, config) {
  const templates = {
    'general_support': {
      title: '🎫 General Support',
      description: 'Thank you for creating a support ticket. Our team will assist you shortly.',
      color: 0x3B82F6,
      priority: 'normal',
      responseTime: '4 hours',
      instructions: [
        '• Please describe your issue clearly and in detail',
        '• Include any relevant screenshots or error messages',
        '• Be patient while we review your request',
        '• Our team will respond as soon as possible'
      ],
      components: []
    },
    
    'technical_support': {
      title: '🔧 Technical Support',
      description: 'Technical support ticket created. Please provide detailed information about the issue.',
      color: 0xFF6B00,
      priority: 'high',
      responseTime: '1 hour',
      instructions: [
        '• Describe the exact problem you\'re experiencing',
        '• Include any error messages (copy/paste or screenshot)',
        '• Tell us what you were trying to do when the issue occurred',
        '• Mention your device/browser if relevant',
        '• List any troubleshooting steps you\'ve already tried'
      ],
      components: []
    },
    
    'user_report': {
      title: '⚠️ User Report',
      description: 'User report submitted. Our moderation team will review this carefully.',
      color: 0xFF0000,
      priority: 'urgent',
      responseTime: '15 minutes',
      instructions: [
        '• Provide the username or user ID of the reported person',
        '• Describe the inappropriate behavior in detail',
        '• Include evidence (screenshots, message links, etc.)',
        '• Specify when and where the incident occurred',
        '• All reports are handled confidentially'
      ],
      components: []
    },
    
    'nsfw_support': {
      title: '🔞 18+ Support',
      description: 'You have opened an 18+ support ticket. To access adult content, you must complete age verification.',
      color: 0x9900FF,
      priority: 'high',
      responseTime: '30 minutes',
      instructions: [
        '**📋 Age Verification Requirements:**',
        '',
        '**1️⃣ Government-Issued Photo ID:**',
        '• Driver\'s License, State ID, Passport, or Military ID',
        '• **BLUR OR BLACK OUT:** Address, ID numbers, signatures',
        '• **KEEP VISIBLE:** Your face, and date of birth',
        '• Must clearly show you are 18+ years old',
        '',
        '**2️⃣ Selfie Holding Your ID:**',
        '• Take a selfie of yourself holding the same ID',
        '• Your face must be clearly visible in the selfie',
        '• The ID must be readable in your hand',
        '• This proves the ID belongs to you and isn\'t stolen',
        '',
        '**🔒 Privacy & Security:**',
        '• Only senior staff will view your verification',
        '• All images are deleted immediately after verification',
        '• We never store or share your personal information',
        '• Verification typically takes 5-15 minutes',
        '',
        '**⚠️ Important Warnings:**',
        '• **Must be 18+** - No exceptions, permanent ban if underage',
        '• **Zero tolerance for fake IDs** - Permanent ban',
        '• **One verification per person** - Alt accounts will be banned',
        '• **Staff decision is final** - Appeals handled by senior staff only'
      ],
      components: []
    }
  };

  return templates[categoryId] || templates['general_support'];
}

/**
 * Create template embed for a ticket
 * @param {string} categoryId - Category ID
 * @param {Object} config - Configuration object
 * @param {Object} ticketData - Ticket data
 * @returns {Object} Embed and components
 */
export function createTicketTemplateEmbed(categoryId, config, ticketData) {
  const template = getTicketTemplate(categoryId, config);
  
  const priorityEmojis = {
    urgent: '🔴',
    high: '🟠', 
    normal: '🟡',
    low: '🟢'
  };

  const embed = new EmbedBuilder()
    .setTitle(template.title)
    .setDescription(template.description)
    .setColor(template.color)
    .addFields({
      name: '📋 Instructions',
      value: template.instructions.join('\n').substring(0, 1024),
      inline: false
    });

  // Check if the instructions value was truncated
  if (template.instructions.join('\n').length > 1024) {
    logger.warn('Ticket instructions truncated due to Discord embed field limit', {
      categoryId,
      originalLength: template.instructions.join('\n').length,
      truncatedLength: 1024
    });
  }

  embed.addFields({
      name: '🎫 Ticket Information',
      value: `**Ticket ID:** #${ticketData.id}\n**Category:** ${ticketData.categoryName}\n**Priority:** ${priorityEmojis[template.priority]} ${template.priority.charAt(0).toUpperCase() + template.priority.slice(1)}\n**Expected Response Time:** ⏰ ${template.responseTime}\n**Created:** <t:${Math.floor(ticketData.createdAt.getTime() / 1000)}:R>`,
      inline: false
    })
    .setTimestamp()
    .setFooter({ text: 'Please follow the instructions above for the best support experience' });

  // Create components if template has them
  const components = [];
  if (template.components && template.components.length > 0) {
    const actionRow = new ActionRowBuilder();
    
    template.components.forEach(comp => {
      if (comp.type === 'button') {
        const button = new ButtonBuilder()
          .setCustomId(comp.customId)
          .setLabel(comp.label)
          .setStyle(comp.style);
        
        if (comp.emoji) {
          button.setEmoji(comp.emoji);
        }
        
        actionRow.addComponents(button);
      }
    });
    
    if (actionRow.components.length > 0) {
      components.push(actionRow);
    }
  }

  return { embed, components, priority: template.priority, responseTime: template.responseTime };
}

 