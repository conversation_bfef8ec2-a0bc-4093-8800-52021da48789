import { AttachmentBuilder } from 'discord.js';
import { Buffer } from 'buffer';
import Logger from '../../../utils/logger.js';

const logger = new Logger({ level: 'info' });

/**
 * Generate HTML transcript for a ticket
 * @param {Collection} messages - Discord messages collection
 * @param {Object} channel - Discord channel object
 * @param {Object} ticketData - Ticket information
 * @param {Object} guild - Discord guild object
 * @returns {string} HTML transcript
 */
export function generateHTMLTranscript(messages, channel, ticketData, guild) {
  const timestamp = new Date().toISOString();
  const sortedMessages = Array.from(messages.values()).sort((a, b) => a.createdTimestamp - b.createdTimestamp);
  
  let html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket #${ticketData.id} Transcript</title>
    <style>
        * { box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #36393f 0%, #2f3136 100%);
            color: #dcddde; 
            margin: 0; 
            padding: 20px;
            line-height: 1.4;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { 
            background: linear-gradient(135deg, #5865f2 0%, #7289da 100%);
            padding: 30px; 
            border-radius: 12px; 
            margin-bottom: 30px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .header h1 { margin: 0 0 10px 0; font-size: 2.2em; color: white; }
        .header-info { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px; }
        .info-item { background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; }
        .info-label { font-weight: bold; color: #ffffff; font-size: 0.9em; text-transform: uppercase; }
        .info-value { margin-top: 5px; color: #e6e6e6; }
        
        .message { 
            margin-bottom: 20px; 
            padding: 20px; 
            background: #40444b; 
            border-radius: 12px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transition: transform 0.2s ease;
        }
        .message:hover { transform: translateY(-2px); }
        
        .message-header { display: flex; align-items: center; margin-bottom: 12px; }
        .avatar { 
            width: 40px; 
            height: 40px; 
            border-radius: 50%; 
            margin-right: 12px; 
            background: #5865f2;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
        .author { 
            font-weight: bold; 
            color: #7289da; 
            font-size: 1.1em;
        }
        .timestamp { 
            color: #72767d; 
            font-size: 0.85em; 
            margin-left: auto;
            background: rgba(0,0,0,0.2);
            padding: 4px 8px;
            border-radius: 4px;
        }
        .content { 
            margin-top: 10px; 
            word-wrap: break-word;
            white-space: pre-wrap;
        }
        .attachment { 
            background: linear-gradient(135deg, #2f3136 0%, #36393f 100%);
            padding: 15px; 
            border-radius: 8px; 
            margin-top: 10px;
            border-left: 4px solid #7289da;
        }
        .attachment a { color: #7289da; text-decoration: none; }
        .attachment a:hover { text-decoration: underline; }
        
        .embed { 
            border-left: 4px solid #7289da; 
            padding: 15px; 
            background: linear-gradient(135deg, #2f3136 0%, #36393f 100%);
            margin-top: 10px; 
            border-radius: 0 8px 8px 0;
        }
        .embed-title { font-weight: bold; color: #ffffff; margin-bottom: 8px; }
        .embed-description { color: #dcddde; }
        
        .system { 
            background: linear-gradient(135deg, #5865f2 0%, #7289da 100%);
            color: white; 
            border-left: 4px solid #ffffff;
        }
        .bot { 
            background: linear-gradient(135deg, #57f287 0%, #3ba55d 100%);
            color: white;
        }
        
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); 
            gap: 15px; 
            margin-top: 30px;
        }
        .stat-card { 
            background: #40444b; 
            padding: 20px; 
            border-radius: 12px; 
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .stat-number { font-size: 2em; font-weight: bold; color: #7289da; }
        .stat-label { color: #72767d; font-size: 0.9em; text-transform: uppercase; }
        
        .footer { 
            text-align: center; 
            margin-top: 40px; 
            padding: 20px; 
            color: #72767d; 
            border-top: 1px solid #40444b;
        }
        
        @media (max-width: 768px) {
            .header-info { grid-template-columns: 1fr; }
            .message-header { flex-direction: column; align-items: flex-start; }
            .timestamp { margin-left: 0; margin-top: 5px; }
        }
        
        .priority-urgent { border-left-color: #ff0000 !important; }
        .priority-high { border-left-color: #ff6b00 !important; }
        .priority-medium { border-left-color: #3b82f6 !important; }
        .priority-low { border-left-color: #10b981 !important; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎫 Ticket #${ticketData.id} Transcript</h1>
            <div class="header-info">
                <div class="info-item">
                    <div class="info-label">Channel</div>
                    <div class="info-value">#${channel.name}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Category</div>
                    <div class="info-value">${ticketData.categoryName || 'Unknown'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Priority</div>
                    <div class="info-value">${ticketData.priority || 'normal'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Status</div>
                    <div class="info-value">${ticketData.status || 'unknown'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Created</div>
                    <div class="info-value">${new Date(ticketData.createdAt).toLocaleString()}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Generated</div>
                    <div class="info-value">${new Date(timestamp).toLocaleString()}</div>
                </div>
            </div>
        </div>
        
        <div class="messages">`;

  // Process messages
  let userMessages = 0;
  let staffMessages = 0;
  let attachmentCount = 0;
  
  sortedMessages.forEach(msg => {
    const isSystem = msg.author.bot || msg.webhookId;
    const isStaff = msg.member?.roles.cache.some(role => 
      ticketData.staffRoles?.includes(role.id)
    );
    
    if (!isSystem) {
      if (isStaff) staffMessages++;
      else userMessages++;
    }
    
    if (msg.attachments.size > 0) attachmentCount += msg.attachments.size;
    
    let messageClass = 'message';
    if (isSystem) messageClass += ' system';
    else if (msg.author.bot) messageClass += ' bot';
    
    const priorityClass = ticketData.priority ? `priority-${ticketData.priority}` : '';
    
    const authorInitial = (msg.author.displayName || msg.author.username).charAt(0).toUpperCase();
    
    html += `
            <div class="${messageClass} ${priorityClass}">
                <div class="message-header">
                    <div class="avatar">${authorInitial}</div>
                    <div class="author">${msg.author.displayName || msg.author.username}</div>
                    <div class="timestamp">${msg.createdAt.toLocaleString()}</div>
                </div>
                <div class="content">${msg.content ? escapeHtml(msg.content) : '<em>No text content</em>'}</div>`;

    // Handle attachments
    if (msg.attachments.size > 0) {
      msg.attachments.forEach((attachment) => {
        html += `<div class="attachment">📎 <a href="${attachment.url}" target="_blank">${escapeHtml(attachment.name)}</a> (${formatFileSize(attachment.size)})</div>`;
      });
    }

    // Handle embeds
    if (msg.embeds.length > 0) {
      msg.embeds.forEach((embed) => {
        html += `<div class="embed">`;
        if (embed.title) html += `<div class="embed-title">${escapeHtml(embed.title)}</div>`;
        if (embed.description) html += `<div class="embed-description">${escapeHtml(embed.description)}</div>`;
        html += `</div>`;
      });
    }

    html += '</div>';
  });

  // Add statistics
  html += `
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${sortedMessages.length}</div>
                <div class="stat-label">Total Messages</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${userMessages}</div>
                <div class="stat-label">User Messages</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${staffMessages}</div>
                <div class="stat-label">Staff Messages</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${attachmentCount}</div>
                <div class="stat-label">Attachments</div>
            </div>
        </div>
        
        <div class="footer">
            <p>Generated by ${guild.name} Support System</p>
            <p>Ticket #${ticketData.id} | ${new Date().toLocaleDateString()}</p>
        </div>
    </div>
</body>
</html>`;

  return html;
}

/**
 * Create transcript attachment from HTML
 * @param {string} html - HTML content
 * @param {string} ticketId - Ticket ID for filename
 * @returns {AttachmentBuilder} Discord attachment
 */
export function createTranscriptAttachment(html, ticketId) {
  const buffer = Buffer.from(html, 'utf8');
  return new AttachmentBuilder(buffer, { 
    name: `ticket-${ticketId}-transcript.html`,
    description: `HTML transcript for ticket #${ticketId}`
  });
}

/**
 * Generate and send transcript
 * @param {Object} interaction - Discord interaction
 * @param {Object} ticketData - Ticket data
 * @param {Object} config - Configuration
 */
export async function generateAndSendTranscript(interaction, ticketData, config) {
  try {
    await interaction.deferReply({ ephemeral: true });
    
    // Fetch all messages from the ticket channel
    const messages = await interaction.channel.messages.fetch({ limit: 100 });
    
    // Generate HTML transcript
    const html = generateHTMLTranscript(messages, interaction.channel, ticketData, interaction.guild);
    
    // Create attachment
    const attachment = createTranscriptAttachment(html, ticketData.id);
    
    // Send transcript
    await interaction.editReply({
      content: `📄 **Ticket #${ticketData.id} Transcript Generated**\n\nThe transcript has been generated and attached below. You can download and view it in any web browser.`,
      files: [attachment]
    });
    
    logger.info('Transcript generated successfully', {
      ticketId: ticketData.id,
      messageCount: messages.size,
      userId: interaction.user.id
    });
    
  } catch (error) {
    logger.error('Failed to generate transcript', {
      error: error.message,
      ticketId: ticketData?.id,
      userId: interaction.user.id
    });
    
    await interaction.editReply({
      content: '❌ Failed to generate transcript. Please try again later.'
    });
  }
}

/**
 * Escape HTML characters
 * @param {string} text - Text to escape
 * @returns {string} Escaped text
 */
function escapeHtml(text) {
  const map = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  };
  return text.replace(/[&<>"']/g, (m) => map[m]);
}

/**
 * Format file size
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
} 