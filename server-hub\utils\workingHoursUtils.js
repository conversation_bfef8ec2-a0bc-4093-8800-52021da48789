import Logger from '../../../utils/logger.js';

const logger = new Logger({ level: 'info' });

/**
 * Default working hours configuration
 * Times are in 24-hour format (0-23)
 * Days: 0 = Sunday, 1 = Monday, ..., 6 = Saturday
 */
const DEFAULT_WORKING_HOURS = {
  enabled: true,
  timezone: 'America/New_York', // EST/EDT
  schedule: {
    monday: { enabled: true, start: 9, end: 17 },    // 9 AM - 5 PM
    tuesday: { enabled: true, start: 9, end: 17 },
    wednesday: { enabled: true, start: 9, end: 17 },
    thursday: { enabled: true, start: 9, end: 17 },
    friday: { enabled: true, start: 9, end: 17 },
    saturday: { enabled: false, start: 0, end: 0 },  // Closed
    sunday: { enabled: false, start: 0, end: 0 }     // Closed
  },
  emergencyCategories: ['user_report'], // Categories that bypass working hours
  closedMessage: {
    title: '🕐 Support Hours',
    description: 'Our support team is currently offline.',
    color: 0xFF6B00,
    fields: [
      {
        name: '🕒 Business Hours',
        value: 'Monday - Friday: 9:00 AM - 5:00 PM EST\nSaturday - Sunday: Closed',
        inline: false
      },
      {
        name: '🚨 Emergency Support',
        value: 'For urgent matters (user reports), support is available 24/7.',
        inline: false
      },
      {
        name: '📅 Next Available',
        value: 'Support will resume during our next business hours.',
        inline: false
      }
    ]
  }
};

/**
 * Get working hours configuration from config or use defaults
 * @param {Object} config - Server configuration
 * @returns {Object} Working hours configuration
 */
export function getWorkingHoursConfig(config) {
  const workingHours = config?.settings?.features?.tickets?.workingHours;
  
  if (!workingHours) {
    throw new Error('Working hours configuration is missing. Please configure working hours in config.yml. ' + 
      'Detailed config path: config.settings.features.tickets.workingHours');
  }
  
  // Require all essential properties to be present
  if (!workingHours.schedule) {
    throw new Error('Working hours schedule is missing. Please define a complete schedule in config.yml.');
  }
  
  // Completely use custom configuration without any fallback
  return {
    enabled: workingHours.enabled,
    timezone: workingHours.timezone,
    schedule: workingHours.schedule,
    emergencyCategories: workingHours.emergencyCategories,
    closedMessage: workingHours.closedMessage
  };
}

/**
 * Check if current time is within working hours
 * @param {Object} config - Server configuration
 * @returns {Object} Working hours status
 */
export function isWithinWorkingHours(config) {
  const workingHours = getWorkingHoursConfig(config);
  
  // If working hours are disabled, always allow tickets
  if (!workingHours.enabled) {
    return {
      isOpen: true,
      reason: 'Working hours disabled - 24/7 support',
      nextOpenTime: null
    };
  }
  
  try {
    const now = new Date();
    const timezone = workingHours.timezone || 'America/New_York';
    
    // Convert to the configured timezone
    const localTime = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
    const dayOfWeek = localTime.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const currentHour = localTime.getHours();
    
    // Get day names for lookup
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const currentDayName = dayNames[dayOfWeek];
    const daySchedule = workingHours.schedule[currentDayName];
    
    // Check if the day is enabled for support OR if start/end are 0 (closed)
    if (!daySchedule || !daySchedule.enabled || (daySchedule.start === 0 && daySchedule.end === 0)) {
      const nextOpenTime = getNextOpenTime(workingHours, timezone);
      return {
        isOpen: false,
        reason: `Support is closed on ${currentDayName}s`,
        nextOpenTime,
        currentTime: localTime,
        timezone
      };
    }
    
    // Check if current hour is within working hours
    const isWithinHours = currentHour >= daySchedule.start && currentHour < daySchedule.end;
    
    if (isWithinHours) {
      return {
        isOpen: true,
        reason: 'Within business hours',
        currentTime: localTime,
        timezone,
        closesAt: `${daySchedule.end}:00`
      };
    } else {
      const nextOpenTime = getNextOpenTime(workingHours, timezone);
      return {
        isOpen: false,
        reason: `Outside business hours (${daySchedule.start}:00 - ${daySchedule.end}:00)`,
        nextOpenTime,
        currentTime: localTime,
        timezone
      };
    }
    
  } catch (error) {
    logger.error('Error checking working hours', {
      error: error.message,
      timezone: workingHours.timezone
    });
    
    // Default to open if there's an error
    return {
      isOpen: true,
      reason: 'Error checking working hours - defaulting to open',
      error: error.message
    };
  }
}

/**
 * Check if a category bypasses working hours (emergency categories)
 * @param {string} categoryId - Ticket category ID
 * @param {Object} config - Server configuration
 * @returns {boolean} Whether category bypasses working hours
 */
export function isCategoryEmergency(categoryId, config) {
  const workingHours = getWorkingHoursConfig(config);
  return workingHours.emergencyCategories.includes(categoryId);
}

/**
 * Get the next time support will be open
 * @param {Object} workingHours - Working hours configuration
 * @param {string} timezone - Timezone string
 * @returns {Date|null} Next open time or null if always closed
 */
function getNextOpenTime(workingHours, timezone) {
  try {
    const now = new Date();
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    
    // Check next 7 days to find next open time
    for (let daysAhead = 0; daysAhead < 7; daysAhead++) {
      const checkDate = new Date(now);
      checkDate.setDate(checkDate.getDate() + daysAhead);
      
      const localCheckDate = new Date(checkDate.toLocaleString('en-US', { timeZone: timezone }));
      const dayOfWeek = localCheckDate.getDay();
      const dayName = dayNames[dayOfWeek];
      const daySchedule = workingHours.schedule[dayName];
      
      // Check if day is actually open (enabled AND not 0,0 hours)
      if (daySchedule && daySchedule.enabled && !(daySchedule.start === 0 && daySchedule.end === 0)) {
        // If it's today, check if we haven't passed the start time yet
        if (daysAhead === 0) {
          const currentHour = localCheckDate.getHours();
          if (currentHour < daySchedule.start) {
            // Still today, before start time
            const nextOpen = new Date(localCheckDate);
            nextOpen.setHours(daySchedule.start, 0, 0, 0);
            return nextOpen;
          }
          // Already past start time today, check tomorrow
          continue;
        } else {
          // Future day
          const nextOpen = new Date(localCheckDate);
          nextOpen.setHours(daySchedule.start, 0, 0, 0);
          return nextOpen;
        }
      }
    }
    
    return null; // No open days found in next week
  } catch (error) {
    logger.error('Error calculating next open time', { error: error.message });
    return null;
  }
}

/**
 * Format working hours status for display
 * @param {Object} status - Working hours status from isWithinWorkingHours
 * @param {Object} config - Server configuration
 * @returns {Object} Formatted display information
 */
export function formatWorkingHoursStatus(status, config) {
  const workingHours = getWorkingHoursConfig(config);
  
  if (status.isOpen) {
    let closesText = '';
    if (status.closesAt) {
      closesText = ` (closes at ${status.closesAt})`;
    }
    
    return {
      emoji: '🟢',
      title: 'Support Available',
      description: `Our support team is currently online and ready to help!${closesText}`,
      color: 0x10B981
    };
  } else {
    let nextOpenText = 'Please check back during business hours.';
    if (status.nextOpenTime) {
      const timestamp = Math.floor(status.nextOpenTime.getTime() / 1000);
      nextOpenText = `Support resumes <t:${timestamp}:R> (<t:${timestamp}:F>)`;
    }
    
    // Generate dynamic business hours display
    const businessHours = getBusinessHoursDisplay(config);
    
    // Create dynamic fields with actual schedule
    const dynamicFields = [
      {
        name: '🕒 Business Hours',
        value: businessHours,
        inline: false
      },
      {
        name: '🚨 Emergency Support',
        value: `For urgent matters (${workingHours.emergencyCategories.join(', ')}), support is available 24/7.`,
        inline: false
      },
      {
        name: '📅 Next Available',
        value: nextOpenText,
        inline: false
      }
    ];
    
    return {
      emoji: '🔴',
      title: 'Support Offline',
      description: `Our support team is currently offline. ${nextOpenText}`,
      color: 0xFF6B00,
      fields: dynamicFields
    };
  }
}

/**
 * Get business hours schedule for display
 * @param {Object} config - Server configuration
 * @returns {string} Formatted schedule
 */
export function getBusinessHoursDisplay(config) {
  const workingHours = getWorkingHoursConfig(config);
  
  if (!workingHours.enabled) {
    return '24/7 Support Available';
  }
  
  const schedule = workingHours.schedule;
  const timezone = workingHours.timezone || 'America/New_York';
  
  let display = '';
  const dayGroups = [];
  
  // Group consecutive days with same hours
  const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  const dayKeys = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  
  for (let i = 0; i < dayKeys.length; i++) {
    const dayKey = dayKeys[i];
    const daySchedule = schedule[dayKey];
    
    // Check if day is actually open (enabled AND not 0,0 hours)
    if (daySchedule && daySchedule.enabled && !(daySchedule.start === 0 && daySchedule.end === 0)) {
      const startTime = daySchedule.start.toString().padStart(2, '0') + ':00';
      const endTime = daySchedule.end.toString().padStart(2, '0') + ':00';
      const hours = `${startTime} - ${endTime}`;
      dayGroups.push(`${dayNames[i]}: ${hours}`);
    } else {
      dayGroups.push(`${dayNames[i]}: Closed`);
    }
  }
  
  display = dayGroups.join('\n');
  display += `\n\n*All times in ${timezone}*`;
  
  return display;
} 